import type { NextRequest } from 'next/server';
import { AppConfig, browserLanguageToLocaleMap, countryToLocaleMap } from './AppConfig';

/**
 * Detect user's preferred locale based on various factors
 */
export function detectLocale(request: NextRequest): string {
  // 1. Check if locale is already in URL path
  const pathname = request.nextUrl.pathname;
  const pathLocale = pathname.split('/')[1];
  if (pathLocale && AppConfig.locales.includes(pathLocale)) {
    return pathLocale;
  }

  // 2. Check for saved locale preference in cookies
  const savedLocale = request.cookies.get('preferred-locale')?.value;
  if (savedLocale && AppConfig.locales.includes(savedLocale)) {
    return savedLocale;
  }

  // 3. Try to detect country from headers (Cloudflare, Vercel, etc.)
  const countryFromHeader = detectCountryFromHeaders(request);
  if (countryFromHeader) {
    const localeFromCountry = countryToLocaleMap[countryFromHeader];
    if (localeFromCountry && AppConfig.locales.includes(localeFromCountry)) {
      return localeFromCountry;
    }
  }

  // 4. Check Accept-Language header
  const acceptLanguage = request.headers.get('accept-language');
  if (acceptLanguage) {
    const localeFromBrowser = detectLocaleFromAcceptLanguage(acceptLanguage);
    if (localeFromBrowser && AppConfig.locales.includes(localeFromBrowser)) {
      return localeFromBrowser;
    }
  }

  // 5. Fallback to default locale
  return AppConfig.defaultLocale;
}

/**
 * Detect country from various headers
 */
function detectCountryFromHeaders(request: NextRequest): string | null {
  // Try different header sources for country detection
  const headers = [
    'cf-ipcountry', // Cloudflare
    'x-vercel-ip-country', // Vercel
    'x-country-code', // Generic
    'x-forwarded-country', // Some CDNs
  ];

  for (const header of headers) {
    const country = request.headers.get(header);
    if (country && country.length === 2) {
      return country.toUpperCase();
    }
  }

  return null;
}

/**
 * Parse Accept-Language header and find best matching locale
 */
function detectLocaleFromAcceptLanguage(acceptLanguage: string): string | null {
  // Parse Accept-Language header
  // Example: "en-US,en;q=0.9,vi;q=0.8,fr;q=0.7"
  const languages = acceptLanguage
    .split(',')
    .map((lang) => {
      const [language, qValue] = lang.trim().split(';q=');
      return {
        language: language?.trim() || '',
        quality: qValue ? Number.parseFloat(qValue) : 1.0,
      };
    })
    .filter(item => item.language) // Filter out empty languages
    .sort((a, b) => b.quality - a.quality); // Sort by quality (preference)

  // Try to match languages with our supported locales
  for (const { language } of languages) {
    // Direct match
    if (AppConfig.locales.includes(language)) {
      return language;
    }

    // Check browser language mapping
    const mappedLocale = browserLanguageToLocaleMap[language];
    if (mappedLocale && AppConfig.locales.includes(mappedLocale)) {
      return mappedLocale;
    }

    // Check language prefix (e.g., "en" from "en-US")
    const languagePrefix = language.split('-')[0];
    if (languagePrefix && AppConfig.locales.includes(languagePrefix)) {
      return languagePrefix;
    }

    // Check if language prefix has a mapping
    if (languagePrefix) {
      const prefixMappedLocale = browserLanguageToLocaleMap[languagePrefix];
      if (prefixMappedLocale && AppConfig.locales.includes(prefixMappedLocale)) {
        return prefixMappedLocale;
      }
    }
  }

  return null;
}

/**
 * Get user's timezone from request headers
 */
export function detectTimezone(request: NextRequest): string | null {
  return request.headers.get('x-timezone') || null;
}

/**
 * Check if user is likely from Vietnam based on various signals
 */
export function isLikelyVietnameseUser(request: NextRequest): boolean {
  // Check country
  const country = detectCountryFromHeaders(request);
  if (country === 'VN') {
    return true;
  }

  // Check Accept-Language for Vietnamese
  const acceptLanguage = request.headers.get('accept-language');
  if (acceptLanguage && acceptLanguage.includes('vi')) {
    return true;
  }

  return false;
}

/**
 * Get locale display name for UI
 */
export function getLocaleDisplayName(locale: string): string {
  const displayNames: Record<string, string> = {
    en: 'English',
    vi: 'Tiếng Việt',
    fr: 'Français',
    th: 'ภาษาไทย',
  };

  return displayNames[locale] || locale.toUpperCase();
}

/**
 * Get locale flag emoji for UI
 */
export function getLocaleFlag(locale: string): string {
  const flags: Record<string, string> = {
    en: '🇺🇸',
    vi: '🇻🇳',
    fr: '🇫🇷',
    th: '🇹🇭',
  };

  return flags[locale] || '🌐';
}
