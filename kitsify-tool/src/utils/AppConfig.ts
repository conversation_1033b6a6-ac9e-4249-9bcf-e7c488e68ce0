import type { LocalePrefixMode } from 'node_modules/next-intl/dist/types/src/routing/types';

const localePrefix: LocalePrefixMode = 'as-needed';

// FIXME: Update this configuration file based on your project information
export const AppConfig = {
  name: 'Kitsify Tools',
  locales: ['en', 'vi', 'fr', 'th'],
  defaultLocale: 'vi',
  localePrefix,
};

// Country to locale mapping for automatic detection
export const countryToLocaleMap: Record<string, string> = {
  // Vietnamese-speaking countries
  VN: 'vi', // Vietnam

  // Thai-speaking countries
  TH: 'th', // Thailand

  // English-speaking countries
  US: 'en', // United States
  GB: 'en', // United Kingdom
  AU: 'en', // Australia
  NZ: 'en', // New Zealand
  IE: 'en', // Ireland
  ZA: 'en', // South Africa
  SG: 'en', // Singapore
  MY: 'en', // Malaysia
  PH: 'en', // Philippines
  IN: 'en', // India
  HK: 'en', // Hong Kong

  // French-speaking countries
  FR: 'fr', // France
  BE: 'fr', // Belgium
  CH: 'fr', // Switzerland
  CA: 'en', // Canada (primarily English, but has French speakers)
  LU: 'fr', // Luxembourg
  MC: 'fr', // Monaco
  SN: 'fr', // Senegal
  CI: 'fr', // Côte d'Ivoire
  ML: 'fr', // Mali
  BF: 'fr', // Burkina Faso
  NE: 'fr', // Niger
  TD: 'fr', // Chad
  MG: 'fr', // Madagascar
  CM: 'fr', // Cameroon
  TG: 'fr', // Togo
  BJ: 'fr', // Benin
  RW: 'fr', // Rwanda
  BI: 'fr', // Burundi
  DJ: 'fr', // Djibouti
  KM: 'fr', // Comoros
  VU: 'fr', // Vanuatu
  NC: 'fr', // New Caledonia
  PF: 'fr', // French Polynesia
  WF: 'fr', // Wallis and Futuna
  PM: 'fr', // Saint Pierre and Miquelon
  RE: 'fr', // Réunion
  YT: 'fr', // Mayotte
  GF: 'fr', // French Guiana
  MQ: 'fr', // Martinique
  GP: 'fr', // Guadeloupe
  BL: 'fr', // Saint Barthélemy
  MF: 'fr', // Saint Martin
};

// Browser language to locale mapping
export const browserLanguageToLocaleMap: Record<string, string> = {
  'vi': 'vi',
  'vi-VN': 'vi',
  'th': 'th',
  'th-TH': 'th',
  'en': 'en',
  'en-US': 'en',
  'en-GB': 'en',
  'en-CA': 'en',
  'en-AU': 'en',
  'fr': 'fr',
  'fr-FR': 'fr',
  'fr-CA': 'fr',
  'fr-BE': 'fr',
  'fr-CH': 'fr',
};
