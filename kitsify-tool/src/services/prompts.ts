import { api } from './api';

export type PromptCategory = {
  id: number;
  name: string;
  description?: string;
  image_url?: string;
  image_card_url?: string;
  prompt_count: number;
  is_coming_soon: boolean;
  created_at: string;
  updated_at: string;
};

export type Prompt = {
  id: number;
  title: string;
  short_description?: string;
  content?: string;
  prompt_text?: string;
  optimization_guide?: string;
  category_id?: number;
  topic_id?: number;
  is_type: number;
  sub_type?: number;
  what_field?: string;
  tips_field?: string;
  how_field?: string;
  input_field?: string;
  output_field?: string;
  add_tip?: string;
  additional_information?: string;
  view_count: number;
  created_at: string;
  updated_at: string;
  category?: PromptCategory;
  topic?: {
    id: number;
    name: string;
    description?: string;
  };
};

// Simplified prompt interface for category listing
export type SimplifiedPrompt = {
  id: number;
  title: string;
  short_description?: string;
};

export type PromptsResponse = {
  data: SimplifiedPrompt[];
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
};

export type QueryPromptsParams = {
  page?: number;
  pageSize?: number;
  category_id?: number;
  search_text?: string;
  lang?: string;
};

export type GeneratePromptParams = {
  prompt_text: string;
  model?: string;
  prompt_id?: number;
};

export type GeneratePromptResponse = {
  result: string;
  model: string;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
};

export type PromptHistory = {
  id: number;
  user_id: number;
  prompt_id?: number;
  generated_result: string;
  model: string;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
  created_at: string;
  prompt?: Prompt;
};

export type PromptHistoriesResponse = {
  data: PromptHistory[];
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
};

export type QueryPromptHistoriesParams = {
  page?: number;
  pageSize?: number;
  prompt_id?: number;
  search_text?: string;
};

class PromptsService {
  async getPromptCategories(lang?: string): Promise<PromptCategory[]> {
    try {
      const params = new URLSearchParams();
      if (lang) {
        params.append('lang', lang);
      }

      const url = `/prompt-categories${params.toString() ? `?${params.toString()}` : ''}`;
      const response = await api.get(url);
      return response.data;
    } catch (error) {
      console.error('Error fetching prompt categories:', error);
      return [];
    }
  }

  async getPromptsByCategory(
    params: QueryPromptsParams,
  ): Promise<PromptsResponse> {
    try {
      const searchParams = new URLSearchParams();

      if (params.page) {
        searchParams.append('page', params.page.toString());
      }
      if (params.pageSize) {
        searchParams.append('pageSize', params.pageSize.toString());
      }
      if (params.category_id) {
        searchParams.append('category_id', params.category_id.toString());
      }
      if (params.search_text) {
        searchParams.append('search_text', params.search_text);
      }
      if (params.lang) {
        searchParams.append('lang', params.lang);
      }

      const response = await api.get(`/prompts/by-category?${searchParams.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching prompts by category:', error);
      return {
        data: [],
        pagination: {
          page: params.page || 1,
          pageSize: params.pageSize || 10,
          total: 0,
          totalPages: 0,
        },
      };
    }
  }

  async getPromptById(id: number, lang?: string): Promise<Prompt | null> {
    try {
      const params = new URLSearchParams();
      if (lang) {
        params.append('lang', lang);
      }

      const url = `/prompts/${id}${params.toString() ? `?${params.toString()}` : ''}`;
      const response = await api.get(url);
      return response.data;
    } catch (error) {
      console.error(`Error fetching prompt with ID ${id}:`, error);
      return null;
    }
  }

  async generatePromptResult(
    params: GeneratePromptParams,
  ): Promise<GeneratePromptResponse | null> {
    try {
      const response = await api.post('/prompts/generate', params);
      return response.data;
    } catch (error) {
      console.error('Error generating prompt result:', error);
      return null;
    }
  }

  async getPromptHistories(
    params: QueryPromptHistoriesParams,
  ): Promise<PromptHistoriesResponse> {
    try {
      const searchParams = new URLSearchParams();

      if (params.page) {
        searchParams.append('page', params.page.toString());
      }
      if (params.pageSize) {
        searchParams.append('pageSize', params.pageSize.toString());
      }
      if (params.prompt_id) {
        searchParams.append('prompt_id', params.prompt_id.toString());
      }
      if (params.search_text) {
        searchParams.append('search_text', params.search_text);
      }

      const response = await api.get(`/prompt-histories?${searchParams.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching prompt histories:', error);
      return {
        data: [],
        pagination: {
          page: params.page || 1,
          pageSize: params.pageSize || 20,
          total: 0,
          totalPages: 0,
        },
      };
    }
  }

  async getPromptHistoryById(id: number): Promise<PromptHistory | null> {
    try {
      const response = await api.get(`/prompt-histories/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching prompt history with ID ${id}:`, error);
      return null;
    }
  }
}

export const promptsService = new PromptsService();
