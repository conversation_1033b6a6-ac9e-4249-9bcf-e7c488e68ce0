import { api } from './api';

export type PackageDuration = {
  id: number;
  package_id: number;
  duration_days: number;
  price: string;
  original_price: string;
  discount_percent: number;
  currency: string;
};

export type PackageFeature = {
  description: string;
  img: string;
};

export type Package = {
  id: number;
  name: string;
  description: string;
  features: PackageFeature[];
  is_best_choice?: boolean;
  has_trail?: boolean;
  has_prompt_library?: boolean;
  has_prompt_video?: boolean;
  sort?: number;
  durations: PackageDuration[];
  accounts: any[];
};

export type PackageUser = {
  id: number;
  user_id: number;
  package_id: number;
  start_date: string;
  expires_at: string;
  status: string;
  created_at: string;
  updated_at: string;
  package: Package;
  user?: any;
};

export type UserPackagesResponse = {
  data: PackageUser[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
};

class PackageService {
  async getPackageDurations(months?: number[], currency: string = 'USD'): Promise<Package[]> {
    try {
      let url = `/public/packages/durations/filter?currency=${currency}`;

      if (months && months.length > 0) {
        url += `&months=${months.join(',')}`;
      }

      const response = await api.get(url);
      return response.data;
    } catch (error) {
      console.error('Error fetching package durations:', error);
      return [];
    }
  }

  async getPackage(id: number): Promise<Package | null> {
    try {
      const response = await api.get(`/public/packages/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching package with ID ${id}:`, error);
      return null;
    }
  }

  async fetchUserPackages(): Promise<UserPackagesResponse | null> {
    try {
      const response = await api.get(`/packages/users/my`);
      return response.data;
    } catch (error) {
      console.error('Error fetching user packages:', error);
      return null;
    }
  }
}

export const packageService = new PackageService();
