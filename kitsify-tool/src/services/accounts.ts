import { api } from './api';

export type Account = {
  id: number;
  website_url: string;
  username?: string;
  password?: string;
  login_cookie: number;
  name: string;
  img_intro?: string;
  last_login?: Date;
  created_at: Date;
  updated_at: Date;
  packages?: any[];
  description?: string;
};

export type AccountsResponse = {
  data: Account[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
};

export const accountsService = {
  /**
   * Fetch all accounts the user has access to
   */
  getAccounts: async (page = 1, limit = 100): Promise<AccountsResponse> => {
    try {
      const response = await api.get(`/accounts/my?page=${page}&limit=${limit}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching accounts:', error);
      throw error;
    }
  },

  /**
   * Fetch a single account by ID
   */
  getAccount: async (id: number): Promise<Account> => {
    try {
      const response = await api.get(`/accounts/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching account ${id}:`, error);
      throw error;
    }
  },

  /**
   * Trigger auto-login for an account
   */
  autoLogin: async (id: number): Promise<any> => {
    try {
      const response = await api.post(`/accounts/${id}/login`, {});
      return response.data;
    } catch (error) {
      console.error(`Error auto-logging in to account ${id}:`, error);
      throw error;
    }
  },
};
