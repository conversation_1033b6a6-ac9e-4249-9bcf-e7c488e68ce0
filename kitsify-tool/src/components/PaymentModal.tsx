import type { Package, PackageDuration } from '@/services/packages';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { packageService } from '@/services/packages';
import { toastError } from '@/utils/toast';
import Notification from '../components/Notification';
import { authService } from '../services/auth';
import { subscriptionService } from '../services/subscription';

type ExtendedPackageDuration = PackageDuration & {
  plan_name?: string;
};

type PaymentModalProps = {
  isOpen: boolean;
  onClose: () => void;
  packageId?: number; // ID of the package to purchase
  durationId?: number | null; // ID of the package duration to purchase
};

export default function PaymentModal({
  isOpen,
  onClose,
  packageId = 1,
  durationId = null,
}: PaymentModalProps) {
  const modalRef = useRef<HTMLDivElement>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [showSuccessNotification, setShowSuccessNotification] = useState(false);
  const [allPackages, setAllPackages] = useState<Package[]>([]);
  const [selectedPackage, setSelectedPackage] = useState<Package | null>(null);
  const [selectedDuration, setSelectedDuration]
    = useState<ExtendedPackageDuration | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isCreatingOrder, setIsCreatingOrder] = useState(false);
  const paypalButtonRendered = useRef(false);
  const isModalOpen = useRef(false);
  const [orderCreated, setOrderCreated] = useState(false);

  // Fetch package durations when modal opens
  useEffect(() => {
    if (!isOpen) {
      isModalOpen.current = false;
      paypalButtonRendered.current = false;
      setOrderCreated(false);
      setIsCreatingOrder(false);
      return;
    }

    isModalOpen.current = true;

    const fetchPackageDurations = async () => {
      setIsLoading(true);
      try {
        // Fetch all packages with their durations (always using USD)
        const packages = await packageService.getPackageDurations(undefined, 'USD');

        // Check if modal is still open before updating state
        if (!isModalOpen.current) {
          return;
        }

        // Filter out packages with has_trail = true
        const filteredPackages = packages.filter(pkg => !pkg.has_trail);
        setAllPackages(filteredPackages);

        // Find the selected package based on packageId from filtered packages
        const selectedPkg = filteredPackages.find(pkg => pkg.id === packageId) || (filteredPackages.length > 0 ? filteredPackages[0] : null);
        if (selectedPkg) {
          setSelectedPackage(selectedPkg);
        }

        // Find the selected duration if durationId is provided
        if (selectedPkg && durationId) {
          const duration = selectedPkg.durations.find(d => d.id === durationId) as ExtendedPackageDuration;
          setSelectedDuration(duration || null);
        } else if (selectedPkg && selectedPkg.durations.length > 0) {
          // Default to the first duration if no durationId is provided
          setSelectedDuration(selectedPkg.durations[0] as ExtendedPackageDuration || null);
        }
      } catch (error) {
        console.error('Error fetching package durations:', error);
      } finally {
        if (isModalOpen.current) {
          setIsLoading(false);
        }
      }
    };

    fetchPackageDurations();
  }, [isOpen, packageId, durationId]);

  // Kiểm tra xác thực khi modal mở
  useEffect(() => {
    if (!isOpen) {
      return;
    }
    const checkAuth = async () => {
      const authStatus = await authService.checkAuthStatus();
      if (isModalOpen.current) {
        setIsAuthenticated(authStatus);
      }
    };
    checkAuth();
  }, [isOpen]);

  // Đóng modal khi click bên ngoài vùng modal
  useEffect(() => {
    if (!isOpen) {
      return;
    }
    const handleClickOutside = (event: MouseEvent) => {
      if (
        modalRef.current
        && !modalRef.current.contains(event.target as Node)
        && !isCreatingOrder // Prevent closing when creating order
      ) {
        onClose();
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isOpen, onClose, isCreatingOrder]);

  // Prevent scrolling when modal is open
  useEffect(() => {
    if (isOpen) {
      // Add overflow-hidden to body when modal opens
      document.body.classList.add('overflow-hidden');
    } else {
      // Remove overflow-hidden from body when modal closes
      document.body.classList.remove('overflow-hidden');
    }

    // Cleanup function to ensure we remove the class when component unmounts
    return () => {
      document.body.classList.remove('overflow-hidden');
    };
  }, [isOpen]);

  // Function to create subscription and render PayPal buttons
  const createSubscriptionAndRenderPayPalButtons = useCallback(async () => {
    try {
      if (!selectedDuration?.id || !isModalOpen.current || paypalButtonRendered.current) {
        return;
      }

      // Set loading state to prevent user interactions
      setIsCreatingOrder(true);

      // Set the flags before creating the order to prevent multiple renders
      paypalButtonRendered.current = true;

      // Clear existing buttons if any
      const container = document.getElementById('paypal-subscription-container');
      if (container) {
        container.innerHTML = '';
      }

      // Create subscription
      const subscriptionResult = await subscriptionService.createSubscription(selectedDuration.id);

      // Check if modal is still open before proceeding
      if (!isModalOpen.current) {
        setIsCreatingOrder(false);
        return;
      }

      if (!subscriptionResult.success) {
        // Reset flags if subscription creation fails
        paypalButtonRendered.current = false;
        setIsCreatingOrder(false);

        // Only show error if modal is still open
        if (isModalOpen.current) {
          toastError(subscriptionResult.error || 'Failed to create subscription. Please try again.');
        }
        return;
      }

      // Set order created state after successful order creation
      setOrderCreated(true);

      // Add a small delay to ensure the DOM has been updated
      setTimeout(() => {
        try {
          // Make sure the container exists
          const container = document.getElementById('paypal-subscription-container');
          if (!container) {
            console.error('PayPal subscription container not found');
            setIsCreatingOrder(false);
            setOrderCreated(false);
            paypalButtonRendered.current = false;
            toastError('Error rendering subscription buttons. Please try again.');
            return;
          }

          // Show the container (in case it was hidden)
          container.classList.remove('hidden');

          // Clear any existing content
          container.innerHTML = '';

          // Render PayPal subscription buttons
          window.paypal.Buttons({
            style: {
              shape: 'rect',
              color: 'gold',
              layout: 'vertical',
              label: 'subscribe',
            },
            createSubscription: () => {
              return subscriptionResult.paypalSubscriptionId;
            },
            onApprove: async (data: { subscriptionID: string }) => {
              try {
                const activateResult = await subscriptionService.activateSubscription(data.subscriptionID);

                // Check if modal is still open before showing success notification
                if (isModalOpen.current) {
                  if (activateResult.success) {
                    onClose();
                    setShowSuccessNotification(true);
                  } else {
                    toastError(activateResult.error || 'Subscription activation failed. Please try again.');
                  }
                }
              } catch {
                if (isModalOpen.current) {
                  toastError('Subscription processing failed. Please try again.');
                }
              }
            },
            onError: (err: Error) => {
              console.error('PayPal subscription error:', err);
              if (isModalOpen.current) {
                toastError('Subscription failed. Please try again.');
                // Reset flag on error
                paypalButtonRendered.current = false;
                setOrderCreated(false);
              }
            },
            onCancel: () => {
              if (isModalOpen.current) {
                toastError('Subscription was cancelled.');
                // Reset flag on cancel
                paypalButtonRendered.current = false;
                setOrderCreated(false);
              }
            },
          }).render('#paypal-subscription-container');

          // Reset loading state after PayPal buttons are rendered
          setIsCreatingOrder(false);
        } catch (error) {
          console.error('Error rendering PayPal buttons:', error);
          setIsCreatingOrder(false);
          setOrderCreated(false);
          paypalButtonRendered.current = false;
          toastError('Error rendering subscription buttons. Please try again.');
        }
      }, 100); // 100ms delay
    } catch {
      // Reset flags on error
      paypalButtonRendered.current = false;
      setOrderCreated(false);
      setIsCreatingOrder(false);

      if (isModalOpen.current) {
        toastError('Subscription processing failed. Please try again.');
      }
    }
  }, [onClose, selectedDuration?.id, setShowSuccessNotification, setOrderCreated, setIsCreatingOrder]);

  // Initialize PayPal SDK when needed
  useEffect(() => {
    // Only initialize PayPal SDK if modal is open and user is authenticated
    if (!isOpen || !isAuthenticated) {
      return;
    }

    // Initialize PayPal SDK if it's not already loaded
    if (!window.paypal) {
      subscriptionService.initializePayPal();
    }
  }, [isOpen, isAuthenticated]);

  // Handle "Create Subscription" button click
  const handleCreateSubscription = useCallback(() => {
    if (!selectedDuration?.id || !isAuthenticated) {
      toastError('Please select a package duration first');
      return;
    }

    // Prevent multiple clicks
    if (isCreatingOrder) {
      return;
    }

    // Set loading state immediately
    setIsCreatingOrder(true);

    // Initialize PayPal SDK if it's not already loaded, then create subscription
    if (!window.paypal) {
      subscriptionService.initializePayPal();

      // Wait for PayPal SDK to load
      const waitForPayPal = setInterval(() => {
        if (window.paypal) {
          clearInterval(waitForPayPal);
          createSubscriptionAndRenderPayPalButtons();
        }
      }, 100);
    } else {
      // PayPal SDK already loaded, create subscription directly
      createSubscriptionAndRenderPayPalButtons();
    }
  }, [selectedDuration?.id, isAuthenticated, isCreatingOrder, setIsCreatingOrder, createSubscriptionAndRenderPayPalButtons]);

  // Handle Google login with message-based authentication
  const handleGoogleLogin = async () => {
    try {
      // Open Google auth window and wait for authentication result
      const success = await authService.loginWithGoogle();

      if (success) {
        // Authentication successful, update state
        setIsAuthenticated(true);

        // Dispatch a custom event to notify other components about the authentication change
        const authChangeEvent = new Event('kitsify-auth-change');
        window.dispatchEvent(authChangeEvent);
      }
    } catch (error) {
      console.error('Google login error:', error);
    }
  };

  // Handle Discord login with message-based authentication
  // const handleDiscordLogin = async () => {
  //   try {
  //     // Open Discord auth window and wait for authentication result
  //     const success = await authService.loginWithDiscord();

  //     if (success) {
  //       // Authentication successful, update state
  //       setIsAuthenticated(true);

  //       // Dispatch a custom event to notify other components about the authentication change
  //       const authChangeEvent = new Event('kitsify-auth-change');
  //       window.dispatchEvent(authChangeEvent);
  //     }
  //   } catch (error) {
  //     console.error('Discord login error:', error);
  //   }
  // };

  // Handle selecting a plan
  const handleSelectPlan = (packageId: number) => {
    const selectedPkg = allPackages.find(pkg => pkg.id === packageId);
    if (selectedPkg) {
      // Only update if it's a different package
      if (selectedPackage?.id !== selectedPkg.id) {
        setSelectedPackage(selectedPkg);
        if (selectedPkg.durations && selectedPkg.durations.length > 0) {
          setSelectedDuration(selectedPkg.durations[0] as ExtendedPackageDuration);

          // Reset order creation state when package changes
          setOrderCreated(false);
          setIsCreatingOrder(false);
          paypalButtonRendered.current = false;

          // Clear existing buttons if any
          const container = document.getElementById('paypal-subscription-container');
          if (container) {
            container.innerHTML = '';
          }
        }
      }
    }
  };

  // Handle selecting a duration
  const handleSelectDuration = (duration: ExtendedPackageDuration) => {
    // Only update if it's a different duration
    if (selectedDuration?.id !== duration.id) {
      setSelectedDuration(duration);
      // Reset order creation state when duration changes
      setOrderCreated(false);
      setIsCreatingOrder(false);
      paypalButtonRendered.current = false;

      // Clear existing buttons if any
      const container = document.getElementById('paypal-subscription-container');
      if (container) {
        container.innerHTML = '';
      }
    }
  };

  // Default price for display (USD only)
  const defaultPrice = '6.00';

  if (!isOpen && !showSuccessNotification) {
    return null;
  }

  return (
    <>
      <style jsx global>
        {`
          @keyframes fadeIn {
            from {
              opacity: 0;
              transform: scale(0.95);
            }
            to {
              opacity: 1;
              transform: scale(1);
            }
          }
        `}
      </style>

      {showSuccessNotification && (
        <Notification
          isOpen={showSuccessNotification}
          onClose={() => setShowSuccessNotification(false)}
        />
      )}
      {isOpen && (
        <div
          id="subscriptionModal"
          className="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto bg-black/70"
        >
          {/* Full-screen loading overlay when creating order */}
          {isCreatingOrder && (
            <div className="absolute inset-0 z-[60] flex items-center justify-center bg-black/50">
              <div className="flex flex-col items-center justify-center space-y-4 rounded-lg bg-white p-8 shadow-xl">
                <div className="size-16 animate-spin rounded-full border-8 border-blue-500 border-t-transparent"></div>
                <p className="text-lg font-medium text-gray-700">Creating your order...</p>
              </div>
            </div>
          )}
          <div
            ref={modalRef}
            className="size-full max-w-5xl overflow-hidden overflow-y-auto rounded-xl bg-white shadow-2xl transition-all duration-300 ease-in-out md:h-auto"
            style={{ animation: 'fadeIn 0.3s ease-in-out' }}
          >
            <div className="flex flex-col lg:flex-row">
              {/* Payment Information Section */}
              <div className="w-full p-8 lg:w-1/2">
                <div className="mb-6 flex items-center justify-between">
                  <h2 className="text-3xl font-bold text-gray-800">Payment Information</h2>
                </div>
                {/* Plan Selection */}
                <div className="mb-8">
                  <h3 className="mb-4 text-xl font-semibold text-gray-700">Choose Your Plan</h3>
                  <div className="grid grid-cols-1 gap-4 sm:grid-cols-2" aria-labelledby="plan-label">
                    {allPackages.map((pkg) => {
                      // Determine if this is the selected package
                      const isSelected = selectedPackage?.id === pkg.id;
                      // Determine if this is the "best value" package based on is_best_choice field
                      const isBestValue = pkg.is_best_choice || false;

                      return (
                        <div
                          key={pkg.id}
                          className={`relative overflow-hidden rounded-xl border-2 p-5 shadow-md transition-all duration-300 ${
                            isSelected
                              ? 'border-blue-500 bg-blue-50 ring-2 ring-blue-300'
                              : 'border-gray-200 bg-white'
                          } ${
                            isCreatingOrder
                              ? 'cursor-not-allowed opacity-60'
                              : 'cursor-pointer hover:border-blue-300 hover:shadow-lg'
                          }`}
                          onClick={() => !isCreatingOrder && handleSelectPlan(pkg.id)}
                          onKeyDown={(e) => {
                            if (!isCreatingOrder && (e.key === 'Enter' || e.key === ' ')) {
                              handleSelectPlan(pkg.id);
                            }
                          }}
                          tabIndex={0}
                          role="button"
                          aria-pressed={isSelected}
                        >
                          <h4 className="mb-2 text-center text-lg font-bold text-gray-800">{pkg.name}</h4>
                          <p className="text-center text-sm text-gray-500">
                            {pkg.id === 1 ? 'Basic features' : pkg.id === 2 ? 'Standard features' : 'All premium features'}
                          </p>

                          {isBestValue && (
                            <div className="absolute -right-12 -top-3 w-40 rotate-45 bg-gradient-to-r from-blue-500 to-cyan-500 py-1 text-center text-xs font-bold text-white shadow-md">
                              BEST VALUE
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                </div>

                {/* Subscription Plans */}
                <div className="mb-6">
                  <h3 className="mb-4 text-xl font-semibold text-gray-700">Select Duration</h3>
                  <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    {selectedPackage?.durations?.map((duration) => {
                      const isSelected = selectedDuration?.id === duration.id;
                      const hasDiscount = duration.discount_percent > 0;

                      return (
                        <div
                          key={duration.id}
                          className={`relative overflow-hidden rounded-xl border-2 p-4 shadow-md transition-all duration-300 ${
                            isSelected
                              ? 'border-blue-500 bg-blue-50 ring-2 ring-blue-300'
                              : 'border-gray-200 bg-white'
                          } ${
                            isCreatingOrder
                              ? 'cursor-not-allowed opacity-60'
                              : 'cursor-pointer hover:border-blue-300 hover:shadow-lg'
                          }`}
                          onClick={() => !isCreatingOrder && handleSelectDuration(duration as ExtendedPackageDuration)}
                          onKeyDown={(e) => {
                            if (!isCreatingOrder && (e.key === 'Enter' || e.key === ' ')) {
                              handleSelectDuration(duration as ExtendedPackageDuration);
                            }
                          }}
                          tabIndex={0}
                          role="button"
                          aria-pressed={isSelected}
                        >
                          <div className="mb-2 text-center text-lg font-medium text-gray-700">
                            {duration.duration_days === 30
                              ? '1 Month'
                              : duration.duration_days === 90
                                ? '3 Months'
                                : duration.duration_days === 180 ? '6 Months' : '12 Months'}
                          </div>
                          <div className="text-center text-2xl font-bold text-blue-600">
                            $
                            {duration.price}
                          </div>

                          {hasDiscount && (
                            <div className="absolute -right-10 -top-10 flex size-20 items-end justify-center rounded-full bg-red-500 pb-3 text-sm font-bold text-white shadow-lg">
                              {duration.discount_percent}
                              % OFF
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>

              {/* Subscription Information Section */}
              <div className="w-full bg-gradient-to-br from-gray-50 to-gray-100 p-8 lg:w-1/2">
                <div className="mb-6 flex items-center justify-between">
                  <h2 className="text-2xl font-bold text-gray-800">Order Summary</h2>
                </div>

                {isLoading
                  ? (
                      <div className="flex h-64 items-center justify-center">
                        <div className="size-12 animate-spin rounded-full border-4 border-blue-500 border-t-transparent"></div>
                      </div>
                    )
                  : (
                      <div className="rounded-xl bg-white p-6 shadow-md">
                        {/* Subscription Details */}
                        <div className="mb-4 flex justify-between border-b border-gray-100 pb-4">
                          <div>
                            <div className="text-sm font-medium text-gray-500">Plan</div>
                            <div className="text-lg font-bold text-gray-800">{selectedPackage?.name || 'Basic'}</div>
                          </div>
                          <div className="text-right">
                            <div className="text-sm font-medium text-gray-500">Duration</div>
                            <div className="text-lg font-bold text-gray-800">
                              {selectedDuration?.duration_days === 30
                                ? '1 Month'
                                : selectedDuration?.duration_days === 90
                                  ? '3 Months'
                                  : selectedDuration?.duration_days === 180 ? '6 Months' : '12 Months'}
                            </div>
                          </div>
                        </div>

                        {/* Monthly Payment */}
                        <div className="mb-6">
                          <div className="flex justify-between">
                            <div className="text-base font-medium text-gray-700">Total</div>
                            <div className="text-2xl font-bold text-blue-600">
                              $
                              {selectedDuration?.price || defaultPrice}
                            </div>
                          </div>

                          {!!selectedDuration?.discount_percent && (
                            <div className="mt-2 text-right text-sm text-green-600">
                              You save
                              {` ${selectedDuration?.discount_percent}`}
                              % with this plan!
                            </div>
                          )}
                        </div>

                        {/* PayPal subscription button container - always present but hidden when not needed */}
                        <div id="paypal-subscription-container" className={`mb-4 ${!isAuthenticated || !orderCreated ? 'hidden' : ''}`}></div>

                        {/* Login Buttons */}
                        {!isAuthenticated
                          ? (
                              <div className="mb-4 space-y-3">
                                {/* Google Login Button */}
                                <button
                                  type="button"
                                  className="w-full rounded-lg border border-gray-300 bg-white px-6 py-3 text-center text-base font-medium text-gray-700 shadow-md transition-all duration-300 hover:bg-gray-50 focus:outline-none focus:ring-4 focus:ring-gray-300"
                                  onClick={handleGoogleLogin}
                                >
                                  <div className="flex items-center justify-center">
                                    <svg
                                      xmlns="http://www.w3.org/2000/svg"
                                      className="mr-2 size-6"
                                      viewBox="0 0 24 24"
                                    >
                                      <path
                                        fill="#4285F4"
                                        d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                                      />
                                      <path
                                        fill="#34A853"
                                        d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                                      />
                                      <path
                                        fill="#FBBC05"
                                        d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                                      />
                                      <path
                                        fill="#EA4335"
                                        d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                                      />
                                    </svg>
                                    Login with Google
                                  </div>
                                </button>

                                {/* Discord Login Button */}
                                {/* <button
                                  type="button"
                                  className="w-full rounded-lg bg-[#5865F2] px-6 py-3 text-center text-base font-medium text-white shadow-md transition-all duration-300 hover:bg-[#4752c4] focus:outline-none focus:ring-4 focus:ring-[#5865F2]"
                                  onClick={handleDiscordLogin}
                                >
                                  <div className="flex items-center justify-center">
                                    <svg className="mr-2 size-6" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 127.14 96.36">
                                      <path fill="#fff" d="M107.7,8.07A105.15,105.15,0,0,0,81.47,0a72.06,72.06,0,0,0-3.36,6.83A97.68,97.68,0,0,0,49,6.83,72.37,72.37,0,0,0,45.64,0,105.89,105.89,0,0,0,19.39,8.09C2.79,32.65-1.71,56.6.54,80.21h0A105.73,105.73,0,0,0,32.71,96.36,77.7,77.7,0,0,0,39.6,85.25a68.42,68.42,0,0,1-10.85-5.18c.91-.66,1.8-1.34,2.66-2a75.57,75.57,0,0,0,64.32,0c.87.71,1.76,1.39,2.66,2a68.68,68.68,0,0,1-10.87,5.19,77,77,0,0,0,6.89,11.1A105.25,105.25,0,0,0,126.6,80.22h0C129.24,52.84,122.09,29.11,107.7,8.07ZM42.45,65.69C36.18,65.69,31,60,31,53s5-12.74,11.43-12.74S54,46,53.89,53,48.84,65.69,42.45,65.69Zm42.24,0C78.41,65.69,73.25,60,73.25,53s5-12.74,11.44-12.74S96.23,46,96.12,53,91.08,65.69,84.69,65.69Z" />
                                    </svg>
                                    Login with Discord
                                  </div>
                                </button> */}
                              </div>
                            )
                          : !orderCreated
                              ? (
                                  <button
                                    type="button"
                                    className={`mb-4 w-full rounded-lg px-6 py-4 text-center text-base font-medium text-white shadow-md transition-all duration-300 focus:outline-none focus:ring-4 ${
                                      isCreatingOrder
                                        ? 'cursor-not-allowed bg-blue-400'
                                        : 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-300'
                                    }`}
                                    onClick={handleCreateSubscription}
                                    disabled={isCreatingOrder}
                                  >
                                    <div className="flex items-center justify-center">
                                      {isCreatingOrder
                                        ? (
                                            <>
                                              <div className="mr-2 size-5 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                                              Creating...
                                            </>
                                          )
                                        : (
                                            'Create Order'
                                          )}
                                    </div>
                                  </button>
                                )
                              : null}

                        <div className="mb-4 text-center text-sm text-gray-500">
                          Please verify your information before proceeding with payment.
                        </div>

                        <div className="text-center">
                          <button
                            type="button"
                            className={`transition-colors ${
                              isCreatingOrder
                                ? 'cursor-not-allowed text-gray-300'
                                : 'text-gray-500 hover:text-red-500'
                            }`}
                            onClick={() => {
                              if (!isCreatingOrder) {
                                isModalOpen.current = false;
                                onClose();
                              }
                            }}
                            disabled={isCreatingOrder}
                          >
                            Cancel Order
                          </button>
                        </div>
                      </div>
                    )}
              </div>
            </div>

            {/* Modal Footer */}
            <div className="border-t border-gray-200 bg-gray-50 px-6 py-4">
              <div className="flex justify-end">
                <button
                  type="button"
                  onClick={() => {
                    if (!isCreatingOrder) {
                      isModalOpen.current = false;
                      onClose();
                    }
                  }}
                  disabled={isCreatingOrder}
                  className={`rounded-lg px-4 py-2 text-sm font-medium text-white shadow-sm transition-all ${
                    isCreatingOrder
                      ? 'cursor-not-allowed bg-gray-300'
                      : 'bg-gray-400 hover:bg-blue-700'
                  }`}
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
