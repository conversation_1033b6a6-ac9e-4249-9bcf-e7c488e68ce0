'use client';

import Color from '@tiptap/extension-color';
import TextStyle from '@tiptap/extension-text-style';
import { EditorContent, useEditor } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import { useEffect } from 'react';

type TipTapViewerProps = {
  content: string;
  className?: string;
  editable?: boolean;
  onContentChange?: (content: string) => void;
};

// Function to preprocess content and convert \n to <br> tags
const preprocessContent = (content: string): string => {
  if (!content) {
    return content;
  }

  // Handle line breaks within HTML tags
  // Replace \n with <br> tags, but be careful not to break HTML structure
  return content.replace(/\n/g, '<br>');
};

export default function TipTapViewer({
  content,
  className = '',
  editable = false,
  onContentChange,
}: TipTapViewerProps) {
  // Preprocess content to handle line breaks
  const processedContent = preprocessContent(content);

  const editor = useEditor({
    extensions: [
      StarterKit,
      TextStyle,
      Color,
    ],
    content: processedContent,
    editable,
    onUpdate: ({ editor }) => {
      if (editable && onContentChange) {
        onContentChange(editor.getHTML());
      }
    },
    editorProps: {
      attributes: {
        class: `prose prose-sm sm:prose lg:prose-lg xl:prose-xl mx-auto focus:outline-none ${
          editable ? 'border border-gray-300 rounded-lg p-4 min-h-[200px]' : ''
        } ${className}`,
      },
    },
  });

  useEffect(() => {
    if (editor) {
      const newProcessedContent = preprocessContent(content);
      if (newProcessedContent !== editor.getHTML()) {
        editor.commands.setContent(newProcessedContent);
      }
    }
  }, [editor, content]);

  if (!editor) {
    return (
      <div className="animate-pulse">
        <div className="mb-2 h-4 w-3/4 rounded bg-gray-200"></div>
        <div className="mb-2 h-4 w-1/2 rounded bg-gray-200"></div>
        <div className="h-4 w-5/6 rounded bg-gray-200"></div>
      </div>
    );
  }

  return (
    <div className={`tiptap-viewer ${className}`}>
      <EditorContent editor={editor} />
    </div>
  );
}
