'use client';

import type { ChangeEventHandler } from 'react';
import { useLocale } from 'next-intl';
import { routing, usePathname, useRouter } from '@/libs/i18nNavigation';
import { getLocaleDisplayName, getLocaleFlag } from '@/utils/localeDetection';

export const LocaleSwitcher = () => {
  const router = useRouter();
  const pathname = usePathname();
  const locale = useLocale();

  const handleChange: ChangeEventHandler<HTMLSelectElement> = (event) => {
    const newLocale = event.target.value;

    // Set cookie to remember user's preference
    document.cookie = `preferred-locale=${newLocale}; max-age=${60 * 60 * 24 * 365}; path=/; samesite=lax`;

    // Use the i18n router which handles locale switching properly
    // pathname from usePathname() already excludes the locale prefix
    router.replace(pathname, { locale: newLocale });
  };

  return (
    <div className="relative">
      <select
        defaultValue={locale}
        onChange={handleChange}
        className="appearance-none rounded-md border border-gray-300 bg-white px-3 py-2 pr-8 font-medium text-gray-700 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
        aria-label="Language switcher"
      >
        {routing.locales.map(localeOption => (
          <option key={localeOption} value={localeOption}>
            {getLocaleFlag(localeOption)}
            {' '}
            {getLocaleDisplayName(localeOption)}
          </option>
        ))}
      </select>

      {/* Custom dropdown arrow */}
      <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
        <svg className="size-4 fill-current" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
          <path d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" />
        </svg>
      </div>
    </div>
  );
};
