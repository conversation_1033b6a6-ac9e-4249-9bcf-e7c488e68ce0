'use client';

import { useEffect, useState } from 'react';
import AuthHeader from '@/components/AuthHeader';
import Sidebar from '@/components/Sidebar';
import { useRouter } from '@/libs/i18nNavigation';
import { authService } from '@/services/auth';

type AuthenticatedLayoutProps = {
  children: React.ReactNode;
};

const AuthenticatedLayout: React.FC<AuthenticatedLayoutProps> = ({ children }) => {
  const router = useRouter();
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const isAuthenticated = await authService.checkAuthStatus();
        setIsAuthenticated(isAuthenticated);
        if (!isAuthenticated) {
          router.push('/sign-in');
        }
      } catch (error) {
        console.error('Error checking authentication:', error);
        router.push('/sign-in');
      }
    };

    checkAuth();
  }, [router]);

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Sidebar */}
      <Sidebar isOpen={isSidebarOpen} toggleSidebar={toggleSidebar} />

      {/* Main Content */}
      <div className="flex flex-1 flex-col overflow-hidden">
        <AuthHeader toggleSidebar={toggleSidebar} />

        {isAuthenticated && (
          <main className="flex-1 overflow-y-auto overflow-x-hidden bg-gray-50 p-4 md:p-6">
            {children}
          </main>
        )}
      </div>
    </div>
  );
};

export default AuthenticatedLayout;
