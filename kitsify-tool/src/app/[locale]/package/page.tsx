'use client';

import { useTranslations } from 'next-intl';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import PaymentModal from '@/components/PaymentModal';
import { packageService } from '@/services/packages';

type PackageUser = {
  id: number;
  package: {
    id: number;
    name: string;
  };
  created_at: string;
  expires_at: string;
};

export default function PackagePage() {
  const t = useTranslations('Package');
  const [userPackages, setUserPackages] = useState<PackageUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedPackageId, setSelectedPackageId] = useState<number>(1); // Default to package ID 1

  useEffect(() => {
    const fetchUserPackages = async () => {
      try {
        setLoading(true);
        const response = await packageService.fetchUserPackages();
        if (response) {
          setUserPackages(response.data);
        }
      } catch (err) {
        console.error('Error fetching user packages:', err);
        toast.error('Failed to load package information');
      } finally {
        setLoading(false);
      }
    };

    fetchUserPackages();
  }, []);

  // Function to format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    }).format(date);
  };

  // Calculate days remaining
  const calculateDaysRemaining = (expiryDate: string) => {
    const today = new Date();
    const expiry = new Date(expiryDate);
    const diffTime = expiry.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays > 0 ? diffDays : 0;
  };

  if (loading) {
    return (
      <div className="flex min-h-[60vh] items-center justify-center">
        <div className="size-12 animate-spin rounded-full border-y-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-800">{t('page_title')}</h1>
        <p className="mt-2 text-gray-600">
          {t('page_title')}
        </p>
      </div>

      {userPackages.length === 0
        ? (
            <div className="rounded-lg bg-white p-6 shadow-md">
              <div className="flex flex-col items-center justify-center py-12 text-center">
                <svg
                  className="mb-4 size-16 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"
                  >
                  </path>
                </svg>
                <h2 className="mb-2 text-xl font-semibold text-gray-700">{t('no_packages')}</h2>
                <p className="mb-6 text-gray-500">
                  {t('no_packages_description')}
                </p>
                <button
                  type="button"
                  onClick={() => {
                    setSelectedPackageId(1); // Default package ID
                    setIsModalOpen(true);
                  }}
                  className="rounded-lg bg-blue-600 px-4 py-2 text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                >
                  {t('browse_packages')}
                </button>
              </div>
            </div>
          )
        : (
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {userPackages.map((pkg: PackageUser) => (
                <div key={pkg.id} className="overflow-hidden rounded-lg bg-white shadow-md transition-all duration-300 hover:shadow-lg">
                  <div className="bg-blue-600 p-4 text-white">
                    <h2 className="text-xl font-bold">{pkg.package.name}</h2>
                  </div>
                  <div className="p-6">
                    <div className="mb-4">
                      <div className="mb-2 flex justify-between">
                        <span className="text-gray-600">
                          {t('status')}
                        </span>
                        <span className={`font-medium ${calculateDaysRemaining(pkg.expires_at) > 0 ? 'text-green-600' : 'text-red-600'}`}>
                          {calculateDaysRemaining(pkg.expires_at) > 0 ? t('active') : t('expired')}
                        </span>
                      </div>
                      <div className="mb-2 flex justify-between">
                        <span className="text-gray-600">
                          {t('purchase_date')}
                        </span>
                        <span className="font-medium">{formatDate(pkg.created_at)}</span>
                      </div>
                      <div className="mb-2 flex justify-between">
                        <span className="text-gray-600">
                          {t('expiry_date')}
                        </span>
                        <span className="font-medium">{formatDate(pkg.expires_at)}</span>
                      </div>
                      <div className="mb-2 flex justify-between">
                        <span className="text-gray-600">
                          {t('days_remaining')}
                        </span>
                        <span className="font-medium">
                          {calculateDaysRemaining(pkg.expires_at)}
                          {' '}
                          {t('days_remaining')}
                        </span>
                      </div>
                    </div>

                    {/* Progress bar for days remaining */}
                    <div className="mb-4">
                      <div className="mb-1 flex justify-between text-xs">
                        <span>{t('progress')}</span>
                        <span>
                          {calculateDaysRemaining(pkg.expires_at)}
                          {' '}
                          {t('days_left')}
                        </span>
                      </div>
                      <div className="h-2 w-full overflow-hidden rounded-full bg-gray-200">
                        <div
                          className="h-full rounded-full bg-blue-600"
                          style={{
                            width: `${Math.min(
                              (calculateDaysRemaining(pkg.expires_at) / 30) * 100,
                              100,
                            )}%`,
                          }}
                        >
                        </div>
                      </div>
                    </div>

                    {calculateDaysRemaining(pkg.expires_at) <= 7 && calculateDaysRemaining(pkg.expires_at) > 0 && (
                      <div className="mb-4 rounded-md bg-yellow-50 p-3 text-sm text-yellow-800">
                        <div className="flex">
                          <svg
                            className="mr-2 size-5 shrink-0"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth="2"
                              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                            >
                            </path>
                          </svg>
                          <span>{t('expiring_soon_warning')}</span>
                        </div>
                      </div>
                    )}

                    {calculateDaysRemaining(pkg.expires_at) <= 0 && (
                      <div className="mb-4 rounded-md bg-red-50 p-3 text-sm text-red-800">
                        <div className="flex">
                          <svg
                            className="mr-2 size-5 shrink-0"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth="2"
                              d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                            >
                            </path>
                          </svg>
                          <span>{t('expired_warning')}</span>
                        </div>
                      </div>
                    )}

                    <button
                      type="button"
                      onClick={() => {
                        // Use the package ID from the current package if available, otherwise default to 1
                        const packageId = pkg.package?.id || 1;
                        setSelectedPackageId(packageId);
                        setIsModalOpen(true);
                      }}
                      className="mt-2 w-full rounded-lg bg-blue-600 px-4 py-2 text-center text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                    >
                      {t('renew_package')}
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
      {/* Payment Modal */}
      <PaymentModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        packageId={selectedPackageId}
      />
    </div>
  );
}
