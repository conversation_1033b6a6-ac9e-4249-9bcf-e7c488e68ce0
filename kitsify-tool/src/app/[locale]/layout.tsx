import type { Metadata, Viewport } from 'next';
import { NextIntlClientProvider } from 'next-intl';
import { getMessages, setRequestLocale } from 'next-intl/server';
import { notFound } from 'next/navigation';
import ClientToastProvider from '@/components/ClientToastProvider';
import GiftButtonWithModal from '@/components/GiftButtonWithModal';
import MessengerButton from '@/components/MessengerButton';
import { routing } from '@/libs/i18nNavigation';
import '@/styles/global.css';

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
};

export const metadata: Metadata = {
  title: 'Kitsify - Auto Login Tool',
  description: 'Kitsify auto login tool for managing your accounts',
  applicationName: 'Kitsify',
  authors: [{ name: 'Kitsify Team' }],
  metadataBase: new URL('https://kitsify.com'),
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://kitsify.com',
    siteName: 'Kitsify',
    title: 'Kitsify - Auto Login Tool',
    description: 'Kitsify auto login tool for managing your accounts',
    images: [
      {
        url: '/assets/images/kitsify_rect.png',
        width: 1200,
        height: 630,
        alt: 'Kitsify Logo',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Kitsify - Auto Login Tool',
    description: 'Kitsify auto login tool for managing your accounts',
    images: ['/assets/images/kitsify_rect.png'],
  },
  icons: [
    {
      rel: 'apple-touch-icon',
      url: '/apple-touch-icon.png',
    },
    {
      rel: 'icon',
      type: 'image/png',
      sizes: '48x48',
      url: '/favicon-48x48.png',
    },
    {
      rel: 'icon',
      type: 'image/png',
      sizes: '32x32',
      url: '/favicon-32x32.png',
    },
    {
      rel: 'icon',
      type: 'image/png',
      sizes: '16x16',
      url: '/favicon-16x16.png',
    },
    {
      rel: 'icon',
      url: '/favicon.ico',
    },
  ],
};

export function generateStaticParams() {
  return routing.locales.map(locale => ({ locale }));
}

export default async function RootLayout(props: {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await props.params;

  if (!routing.locales.includes(locale)) {
    notFound();
  }

  setRequestLocale(locale);

  // Using internationalization in Client Components
  const messages = await getMessages();

  // The `suppressHydrationWarning` attribute in <body> is used to prevent hydration errors caused by Sentry Overlay,
  // which dynamically adds a `style` attribute to the body tag.

  return (
    <html lang={locale} dir="ltr">
      <head>
        <meta charSet="utf-8" />
      </head>
      <body suppressHydrationWarning className="min-h-screen">
        <NextIntlClientProvider
          locale={locale}
          messages={messages}
        >
          <ClientToastProvider>
            {props.children}
            <GiftButtonWithModal />
            <MessengerButton />
          </ClientToastProvider>
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
