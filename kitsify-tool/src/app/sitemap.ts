import type { MetadataRoute } from 'next';
import { getBaseUrl } from '@/libs/Env';
import { AppConfig } from '@/utils/AppConfig';

export default function sitemap(): MetadataRoute.Sitemap {
  const baseUrl = getBaseUrl();

  // Define all public routes
  const routes = [
    '',
    '/privacy-policy',
  ];

  // Generate sitemap entries for all locales and routes
  const entries: MetadataRoute.Sitemap = [];

  // Add default locale routes (without locale prefix)
  routes.forEach((route) => {
    entries.push({
      url: `${baseUrl}${route}`,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: route === '' ? 1.0 : 0.8,
    });
  });

  // Add non-default locale routes (with locale prefix)
  AppConfig.locales.forEach((locale) => {
    if (locale !== AppConfig.defaultLocale) {
      routes.forEach((route) => {
        entries.push({
          url: `${baseUrl}/${locale}${route}`,
          lastModified: new Date(),
          changeFrequency: 'daily',
          priority: route === '' ? 0.9 : 0.7,
        });
      });
    }
  });

  return entries;
}
