import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Package } from './entities/package.entity';
import { PackageUser } from './entities/package-user.entity';
import { PackageDuration } from './entities/package-duration.entity';
import { AuthModule } from '../auth/auth.module';
import { PackagesController } from './packages.controller';
import { PackageUsersController } from './package-users.controller';
import { PublicPackagesController } from './public-packages.controller';
import { PackagesService } from './packages.service';
import { PackageUsersService } from './package-users.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([Package, PackageUser, PackageDuration]),
    AuthModule,
  ],
  controllers: [
    PackagesController,
    PackageUsersController,
    PublicPackagesController,
  ],
  providers: [PackagesService, PackageUsersService],
  exports: [PackagesService, PackageUsersService],
})
export class PackagesModule {}
