import { Injectable, HttpException, Http<PERSON>tatus, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import { Subscription } from './entities/subscription.entity';
import { CreateSubscriptionDto } from './dto/create-subscription.dto';
import { CancelSubscriptionDto } from './dto/cancel-subscription.dto';
import { PayPalPlansService } from './services/paypal-plans.service';
import { PackagesService } from '../packages/packages.service';
import { UsersService } from '../users/users.service';
import { calculateDiscountedPrice } from './utils/pricing.utils';
import { PackageUser } from '../packages/entities/package-user.entity';
import { SubscriptionEmailService } from './services/subscription-email.service';

interface PayPalSubscription {
  id: string;
  status: string;
  status_update_time: string;
  plan_id: string;
  start_time: string;
  quantity: string;
  shipping_amount: {
    currency_code: string;
    value: string;
  };
  subscriber: {
    name: {
      given_name: string;
      surname: string;
    };
    email_address: string;
  };
  billing_info: {
    outstanding_balance: {
      currency_code: string;
      value: string;
    };
    cycle_executions: Array<{
      tenure_type: string;
      sequence: number;
      cycles_completed: number;
      cycles_remaining: number;
      current_pricing_scheme_version: number;
    }>;
    last_payment: {
      amount: {
        currency_code: string;
        value: string;
      };
      time: string;
    };
    next_billing_time: string;
  };
  links: Array<{
    href: string;
    rel: string;
    method: string;
  }>;
}

@Injectable()
export class SubscriptionsService {
  private readonly logger = new Logger(SubscriptionsService.name);
  private baseURL: string;

  constructor(
    @InjectRepository(Subscription)
    private subscriptionRepository: Repository<Subscription>,
    private configService: ConfigService,
    private paypalPlansService: PayPalPlansService,
    private packagesService: PackagesService,
    private _usersService: UsersService,
    private subscriptionEmailService: SubscriptionEmailService,
  ) {
    const isProduction =
      this.configService.get<string>('NODE_ENV') === 'production';
    this.baseURL = !isProduction
      ? 'https://api.paypal.com'
      : 'https://api.sandbox.paypal.com';
  }

  async createSubscription(createSubscriptionDto: CreateSubscriptionDto) {
    try {
      // Get package duration details
      const packageDuration = await this.packagesService.findPackageDuration(
        createSubscriptionDto.package_duration_id,
      );

      if (!packageDuration) {
        throw new HttpException(
          `Package duration with ID ${createSubscriptionDto.package_duration_id} not found`,
          HttpStatus.NOT_FOUND,
        );
      }

      // Get the package details
      const packageEntity = await this.packagesService.findOne(
        packageDuration.package_id,
      );

      if (!packageEntity) {
        throw new HttpException(
          `Package with ID ${packageDuration.package_id} not found`,
          HttpStatus.NOT_FOUND,
        );
      }

      // Calculate discounted price using utility function
      const discountedPrice = calculateDiscountedPrice(
        Number(packageDuration.price),
        packageDuration.discount_percent,
      );

      // Create or get PayPal billing plan
      const plan = await this.paypalPlansService.createBillingPlan(
        packageEntity.name,
        discountedPrice,
        packageDuration.duration_days,
        packageEntity.description,
      );

      // Create PayPal subscription
      const accessToken = await this.paypalPlansService.getAccessToken();

      const subscriptionData = {
        plan_id: plan.id,
        start_time: new Date(Date.now() + 60000).toISOString(), // Start 1 minute from now
        quantity: '1',
        shipping_amount: {
          currency_code: 'USD',
          value: '0.00',
        },
        subscriber: {
          name: {
            given_name: 'Subscriber',
            surname: 'User',
          },
          email_address: '<EMAIL>', // This will be updated with actual user email
        },
        application_context: {
          brand_name: 'Kitsify',
          locale: 'en-US',
          shipping_preference: 'NO_SHIPPING',
          user_action: 'SUBSCRIBE_NOW',
          payment_method: {
            payer_selected: 'PAYPAL',
            payee_preferred: 'IMMEDIATE_PAYMENT_REQUIRED',
          },
          return_url:
            createSubscriptionDto.return_url ||
            `${this.configService.get('APP_URL')}/subscription/success`,
          cancel_url:
            createSubscriptionDto.cancel_url ||
            `${this.configService.get('APP_URL')}/subscription/cancel`,
        },
      };

      const response = await axios.post(
        `${this.baseURL}/v1/billing/subscriptions`,
        subscriptionData,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
            Accept: 'application/json',
            'PayPal-Request-Id': `subscription-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
          },
        },
      );

      const paypalSubscription = response.data as {
        id: string;
        links: Array<{ rel: string; href: string }>;
      };

      // Calculate expiration date based on duration
      const startDate = new Date();
      const expiresAt = new Date(startDate);
      expiresAt.setDate(expiresAt.getDate() + packageDuration.duration_days);

      // Create local subscription record with discounted amount
      const subscription = this.subscriptionRepository.create({
        user_id: createSubscriptionDto.user_id,
        paypal_subscription_id: paypalSubscription.id,
        paypal_plan_id: plan.id,
        package_duration_id: createSubscriptionDto.package_duration_id,
        status: 'pending',
        start_date: startDate,
        expires_at: expiresAt,
        amount: discountedPrice,
        currency: 'USD',
      });

      await this.subscriptionRepository.save(subscription);

      // Find approval URL
      const approvalUrl = paypalSubscription.links.find(
        (link: { rel: string; href: string }) => link.rel === 'approve',
      )?.href;

      return {
        subscriptionId: subscription.id,
        paypalSubscriptionId: paypalSubscription.id,
        approvalUrl,
        planId: plan.id,
        amount: discountedPrice,
        originalPrice: Number(packageDuration.price),
        discountPercent: packageDuration.discount_percent,
        packageId: packageDuration.package_id,
        durationId: packageDuration.id,
        durationDays: packageDuration.duration_days,
      };
    } catch (error) {
      this.logger.error('Failed to create subscription:', error);
      throw new HttpException(
        `Failed to create subscription: ${(error as Error).message || 'Unknown error'}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async activateSubscription(
    _subscriptionId: string,
    paypalSubscriptionId: string,
  ) {
    try {
      const accessToken = await this.paypalPlansService.getAccessToken();

      // Get subscription details from PayPal
      const response = await axios.get(
        `${this.baseURL}/v1/billing/subscriptions/${paypalSubscriptionId}`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
          },
        },
      );

      const paypalSubscription = response.data as PayPalSubscription;

      this.logger.log('PayPal subscription details:', {
        id: paypalSubscription.id,
        status: paypalSubscription.status,
        start_time: paypalSubscription.start_time,
        status_update_time: paypalSubscription.status_update_time,
        plan_id: paypalSubscription.plan_id,
      });

      // Update local subscription
      const subscription = await this.subscriptionRepository.findOne({
        where: { paypal_subscription_id: paypalSubscriptionId },
        relations: ['user', 'packageDuration'],
      });

      if (!subscription) {
        throw new HttpException('Subscription not found', HttpStatus.NOT_FOUND);
      }

      // Handle different subscription statuses
      let localStatus = paypalSubscription.status.toLowerCase();
      let shouldUpdateAccess = false;

      switch (paypalSubscription.status) {
        case 'ACTIVE':
          localStatus = 'active';
          shouldUpdateAccess = true;
          break;
        case 'EXPIRED':
          // If subscription is expired, check if it was due to billing cycle configuration
          this.logger.warn(
            `Subscription ${paypalSubscriptionId} is expired. This might be due to incorrect billing cycle configuration.`,
          );
          localStatus = 'expired';
          break;
        case 'CANCELLED':
          localStatus = 'cancelled';
          break;
        case 'SUSPENDED':
          localStatus = 'suspended';
          break;
        case 'APPROVAL_PENDING':
          localStatus = 'pending';
          break;
        default:
          localStatus = paypalSubscription.status.toLowerCase();
      }

      // Update subscription in database
      const updateData: any = {
        status: localStatus,
        next_billing_date: paypalSubscription.billing_info?.next_billing_time
          ? new Date(paypalSubscription.billing_info.next_billing_time)
          : null,
      };

      // If subscription has a start time, update it
      if (paypalSubscription.start_time) {
        updateData.start_date = new Date(paypalSubscription.start_time);
      }

      await this.subscriptionRepository.update(subscription.id, updateData);

      // Update user's package access if subscription is active
      if (shouldUpdateAccess) {
        await this.updateUserPackageAccess(subscription);

        // Send confirmation email when subscription is activated
        try {
          this.logger.log(
            `Sending subscription confirmation email for activated subscription ${subscription.id}`,
          );

          // Get the updated subscription with all relations for email
          const updatedSubscription = await this.subscriptionRepository.findOne(
            {
              where: { id: subscription.id },
              relations: ['user', 'packageDuration', 'packageDuration.package'],
            },
          );

          if (updatedSubscription && updatedSubscription.user) {
            await this.subscriptionEmailService.sendSubscriptionConfirmationEmail(
              updatedSubscription,
              updatedSubscription.user,
            );
          } else {
            this.logger.error(
              `Could not send email: subscription or user not found for subscription ${subscription.id}`,
            );
          }
        } catch (emailError) {
          this.logger.error(
            `Failed to send subscription confirmation email for subscription ${subscription.id}:`,
            emailError,
          );
          // Don't throw the error to prevent activation failure
        }
      }

      // If subscription is expired, provide additional information
      if (paypalSubscription.status === 'EXPIRED') {
        return {
          success: false,
          status: paypalSubscription.status,
          subscriptionId: subscription.id,
          error: 'Subscription is expired',
          message:
            'The subscription has expired. This might be due to billing cycle configuration. Please create a new subscription.',
          details: {
            start_time: paypalSubscription.start_time,
            status_update_time: paypalSubscription.status_update_time,
            plan_id: paypalSubscription.plan_id,
          },
        };
      }

      return {
        success: true,
        status: paypalSubscription.status,
        subscriptionId: subscription.id,
        message: `Subscription status: ${paypalSubscription.status}`,
      };
    } catch (error) {
      this.logger.error('Failed to activate subscription:', error);
      throw new HttpException(
        `Failed to activate subscription: ${(error as Error).message || 'Unknown error'}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async cancelSubscription(
    subscriptionId: number,
    cancelDto: CancelSubscriptionDto,
  ) {
    try {
      const subscription = await this.subscriptionRepository
        .createQueryBuilder('subscription')
        .leftJoinAndSelect('subscription.user', 'user')
        .select([
          'subscription',
          // User fields - exclude sensitive data
          'user.id',
          'user.email',
          'user.status',
          'user.role',
          'user.created_at',
          'user.updated_at',
        ])
        .where('subscription.id = :id', { id: subscriptionId })
        .getOne();

      if (!subscription) {
        throw new HttpException('Subscription not found', HttpStatus.NOT_FOUND);
      }

      const accessToken = await this.paypalPlansService.getAccessToken();

      // Cancel subscription in PayPal
      await axios.post(
        `${this.baseURL}/v1/billing/subscriptions/${subscription.paypal_subscription_id}/cancel`,
        {
          reason: cancelDto.reason || 'User requested cancellation',
        },
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
          },
        },
      );

      // Update local subscription
      await this.subscriptionRepository.update(subscriptionId, {
        status: 'cancelled',
        cancelled_at: new Date(),
        cancel_reason: cancelDto.reason,
      });

      this.logger.log(`Cancelled subscription: ${subscriptionId}`);
      return { success: true, message: 'Subscription cancelled successfully' };
    } catch (error) {
      this.logger.error('Failed to cancel subscription:', error);
      throw new HttpException(
        'Failed to cancel subscription',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getSubscriptionStatus(subscriptionId: number) {
    const subscription = await this.subscriptionRepository
      .createQueryBuilder('subscription')
      .leftJoinAndSelect('subscription.user', 'user')
      .leftJoinAndSelect('subscription.packageDuration', 'packageDuration')
      .leftJoinAndSelect('packageDuration.package', 'package')
      .select([
        'subscription',
        'packageDuration',
        'package',
        // User fields - exclude sensitive data
        'user.id',
        'user.email',
        'user.status',
        'user.role',
        'user.created_at',
        'user.updated_at',
      ])
      .where('subscription.id = :id', { id: subscriptionId })
      .getOne();

    if (!subscription) {
      throw new HttpException('Subscription not found', HttpStatus.NOT_FOUND);
    }

    return subscription;
  }

  async getUserSubscriptions(userId: number) {
    return this.subscriptionRepository.find({
      where: { user_id: userId },
      relations: ['packageDuration', 'packageDuration.package'],
      order: { created_at: 'DESC' },
    });
  }

  async checkExpiredSubscriptions() {
    const now = new Date();
    const expiredSubscriptions = await this.subscriptionRepository
      .createQueryBuilder('subscription')
      .leftJoinAndSelect('subscription.user', 'user')
      .leftJoinAndSelect('subscription.packageDuration', 'packageDuration')
      .select([
        'subscription',
        'packageDuration',
        // User fields - exclude sensitive data
        'user.id',
        'user.email',
        'user.status',
        'user.role',
        'user.created_at',
        'user.updated_at',
      ])
      .where('subscription.status = :status', { status: 'active' })
      .andWhere('subscription.expires_at <= :now', { now })
      .getMany();

    for (const subscription of expiredSubscriptions) {
      await this.subscriptionRepository.update(subscription.id, {
        status: 'expired',
      });

      this.logger.log(
        `Subscription ${subscription.id} for user ${subscription.user_id} has expired`,
      );

      // Remove user's package access
      await this.removeUserPackageAccess(subscription);
    }

    return expiredSubscriptions.length;
  }

  async renewSubscription(
    subscriptionId: number,
    newPackageDurationId: number,
  ) {
    const subscription = await this.subscriptionRepository
      .createQueryBuilder('subscription')
      .leftJoinAndSelect('subscription.user', 'user')
      .leftJoinAndSelect('subscription.packageDuration', 'packageDuration')
      .select([
        'subscription',
        'packageDuration',
        // User fields - exclude sensitive data
        'user.id',
        'user.email',
        'user.status',
        'user.role',
        'user.created_at',
        'user.updated_at',
      ])
      .where('subscription.id = :id', { id: subscriptionId })
      .getOne();

    if (!subscription) {
      throw new HttpException('Subscription not found', HttpStatus.NOT_FOUND);
    }

    // Get new package duration
    const newPackageDuration =
      await this.packagesService.findPackageDuration(newPackageDurationId);

    if (!newPackageDuration) {
      throw new HttpException(
        'Package duration not found',
        HttpStatus.NOT_FOUND,
      );
    }

    // Calculate new expiration date
    const now = new Date();
    const newExpiresAt = new Date(now);
    newExpiresAt.setDate(
      newExpiresAt.getDate() + newPackageDuration.duration_days,
    );

    // Calculate discounted price for renewal using utility function
    const renewalDiscountedPrice = calculateDiscountedPrice(
      Number(newPackageDuration.price),
      newPackageDuration.discount_percent,
    );

    // Update subscription
    await this.subscriptionRepository.update(subscriptionId, {
      package_duration_id: newPackageDurationId,
      expires_at: newExpiresAt,
      amount: renewalDiscountedPrice,
      status: 'active',
    });

    this.logger.log(`Renewed subscription ${subscriptionId}`);
    return {
      success: true,
      newExpiresAt,
      amount: renewalDiscountedPrice,
      originalPrice: Number(newPackageDuration.price),
      discountPercent: newPackageDuration.discount_percent,
    };
  }

  async reactivateExpiredSubscription(subscriptionId: number) {
    try {
      const subscription = await this.subscriptionRepository
        .createQueryBuilder('subscription')
        .leftJoinAndSelect('subscription.user', 'user')
        .leftJoinAndSelect('subscription.packageDuration', 'packageDuration')
        .select([
          'subscription',
          'packageDuration',
          // User fields - exclude sensitive data
          'user.id',
          'user.email',
          'user.status',
          'user.role',
          'user.created_at',
          'user.updated_at',
        ])
        .where('subscription.id = :id', { id: subscriptionId })
        .getOne();

      if (!subscription) {
        throw new HttpException('Subscription not found', HttpStatus.NOT_FOUND);
      }

      if (subscription.status !== 'expired') {
        throw new HttpException(
          'Only expired subscriptions can be reactivated',
          HttpStatus.BAD_REQUEST,
        );
      }

      // Create a new subscription with the same package duration
      const createSubscriptionDto = {
        user_id: subscription.user_id,
        package_duration_id: subscription.package_duration_id,
        return_url: `${this.configService.get('APP_URL')}/subscription/success`,
        cancel_url: `${this.configService.get('APP_URL')}/subscription/cancel`,
      };

      const newSubscription = await this.createSubscription(
        createSubscriptionDto,
      );

      this.logger.log(
        `Created new subscription ${newSubscription.subscriptionId} to replace expired subscription ${subscriptionId}`,
      );

      return {
        success: true,
        message: 'New subscription created to replace expired one',
        oldSubscriptionId: subscriptionId,
        newSubscription,
      };
    } catch (error) {
      this.logger.error('Failed to reactivate expired subscription:', error);
      throw new HttpException(
        `Failed to reactivate subscription: ${(error as Error).message || 'Unknown error'}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async updateUserPackageAccess(subscription: Subscription) {
    try {
      this.logger.log(
        `Updating package access for user ${subscription.user_id}, subscription ${subscription.id}`,
      );

      // Get the package duration information to find the package_id
      const packageDuration = await this.packagesService.findPackageDuration(
        subscription.package_duration_id,
      );

      if (!packageDuration) {
        this.logger.error(
          `Package duration not found for subscription ${subscription.id}`,
        );
        return;
      }

      // Assign the package to the user using the existing service method
      await this.packagesService.assignToUser(
        packageDuration.package_id,
        subscription.user_id,
        subscription.expires_at,
        subscription.package_duration_id,
      );

      // Update user status to active
      await this._usersService.updateStatus(subscription.user_id, 'active');

      this.logger.log(
        `Successfully updated package access for user ${subscription.user_id}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to update package access for user ${subscription.user_id}:`,
        error,
      );
      throw error;
    }
  }

  async removeUserPackageAccess(subscription: Subscription) {
    try {
      this.logger.log(
        `Removing package access for user ${subscription.user_id}, subscription ${subscription.id}`,
      );

      // Get the package duration information to find the package_id
      const packageDuration = await this.packagesService.findPackageDuration(
        subscription.package_duration_id,
      );

      if (!packageDuration) {
        this.logger.error(
          `Package duration not found for subscription ${subscription.id}`,
        );
        return;
      }

      // Find and update the package user record to expired status
      const packageUser = await this.subscriptionRepository.manager
        .getRepository(PackageUser)
        .findOne({
          where: {
            user_id: subscription.user_id,
            package_id: packageDuration.package_id,
          },
        });

      if (packageUser) {
        await this.subscriptionRepository.manager
          .getRepository(PackageUser)
          .update(packageUser.id, {
            status: 'expired',
          });

        this.logger.log(
          `Updated package user ${packageUser.id} status to expired`,
        );
      }

      // Check if user has any other active subscriptions
      const activeSubscriptions = await this.subscriptionRepository.count({
        where: {
          user_id: subscription.user_id,
          status: 'active',
        },
      });

      // If no other active subscriptions, set user status to inactive
      if (activeSubscriptions === 0) {
        await this._usersService.updateStatus(subscription.user_id, 'inactive');
        this.logger.log(
          `Updated user ${subscription.user_id} status to inactive (no active subscriptions)`,
        );
      }

      this.logger.log(
        `Successfully removed package access for user ${subscription.user_id}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to remove package access for user ${subscription.user_id}:`,
        error,
      );
      throw error;
    }
  }
}
