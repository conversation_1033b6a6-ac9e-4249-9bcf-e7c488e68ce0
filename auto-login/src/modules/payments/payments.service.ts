import { Injectable, HttpException, HttpStatus, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Payment } from './entities/payment.entity';
import { ConfigService } from '@nestjs/config';
import { CreatePaymentDto } from './dto/create-payment.dto';
import * as paypal from '@paypal/checkout-server-sdk';
import { UsersService } from '../users/users.service';
import { PackagesService } from '../packages/packages.service';
import { MailerService } from '@nestjs-modules/mailer';
import { User } from '../users/entities/user.entity';
import { calculateDiscountedPrice } from '../subscriptions/utils/pricing.utils';

@Injectable()
export class PaymentsService {
  private paypalClient: paypal.core.PayPalHttpClient;
  private readonly logger = new Logger(PaymentsService.name);

  constructor(
    @InjectRepository(Payment)
    private paymentRepository: Repository<Payment>,
    private configService: ConfigService,
    private usersService: UsersService,
    private packagesService: PackagesService,
    private mailerService: MailerService,
  ) {
    const clientId = this.configService.get<string>('paypal.clientId');
    const clientSecret = this.configService.get<string>('paypal.clientSecret');
    const environment =
      this.configService.get<string>('NODE_ENV') !== 'production'
        ? new paypal.core.LiveEnvironment(clientId, clientSecret)
        : new paypal.core.SandboxEnvironment(clientId, clientSecret);
    this.paypalClient = new paypal.core.PayPalHttpClient(environment);
  }

  async createPaypalOrder(createPaymentDto: CreatePaymentDto) {
    try {
      // Get package duration details
      const packageDuration = await this.packagesService.findPackageDuration(
        createPaymentDto.package_duration_id,
      );

      if (!packageDuration) {
        throw new HttpException(
          `Package duration with ID ${createPaymentDto.package_duration_id} not found`,
          HttpStatus.NOT_FOUND,
        );
      }

      // Get the package details
      const packageEntity = await this.packagesService.findOne(
        packageDuration.package_id,
      );

      if (!packageEntity) {
        throw new HttpException(
          `Package with ID ${packageDuration.package_id} not found`,
          HttpStatus.NOT_FOUND,
        );
      }

      // Get the base price from the package duration
      const basePrice = packageDuration.price;
      const discountPercent = packageDuration.discount_percent || 0;
      const durationDays = packageDuration.duration_days;

      // Calculate the discounted price using utility function
      const discountedPrice = calculateDiscountedPrice(
        basePrice,
        discountPercent,
      );

      // Format the price to 2 decimal places
      const amount = parseFloat(discountedPrice.toFixed(2));

      const request = new paypal.orders.OrdersCreateRequest();
      request.prefer('return=representation');
      request.requestBody({
        intent: 'CAPTURE',
        purchase_units: [
          {
            amount: {
              currency_code: 'USD',
              value: amount.toString(),
            },
            description: `Payment for package ${packageDuration.package_id} (${durationDays} days)${discountPercent > 0 ? ` with ${discountPercent}% discount` : ''}`,
          },
        ],
      });

      const order = await this.paypalClient.execute(request);

      // Create payment record with package_duration_id and discounted amount
      const payment = this.paymentRepository.create({
        user_id: createPaymentDto.user_id,
        package_duration_id: createPaymentDto.package_duration_id,
        amount: amount,
        payment_date: new Date(),
        status: 'pending',
      });
      await this.paymentRepository.save(payment);

      return {
        orderId: order.result.id,
        paymentId: payment.id,
        packageId: packageDuration.package_id,
        durationId: packageDuration.id,
        durationDays: packageDuration.duration_days,
        price: amount, // Return the discounted price
        originalPrice: basePrice,
        discountPercent: discountPercent,
      };
    } catch (error) {
      console.error('PayPal order creation error:', error);
      if (error.statusCode === 401) {
        throw new HttpException(
          'PayPal authentication failed. Please check your client credentials.',
          HttpStatus.UNAUTHORIZED,
        );
      }
      throw new HttpException(
        'Failed to create PayPal order: ' + (error.message || 'Unknown error'),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async capturePaypalPayment(orderId: string, paymentId: number) {
    try {
      const request = new paypal.orders.OrdersCaptureRequest(orderId);
      const capture = await this.paypalClient.execute(request);

      if (capture.result.status === 'COMPLETED') {
        // Update payment status
        const payment = await this.paymentRepository.findOne({
          where: { id: paymentId },
          relations: ['user'],
        });

        if (!payment) {
          throw new HttpException('Payment not found', HttpStatus.NOT_FOUND);
        }

        await this.paymentRepository.update(paymentId, {
          status: 'completed',
        });

        // Get package duration details
        try {
          // Get the package duration
          const packageDuration =
            await this.packagesService.findPackageDuration(
              payment.package_duration_id,
            );

          if (!packageDuration) {
            throw new Error(
              `Package duration with ID ${payment.package_duration_id} not found`,
            );
          }

          // Get the package
          const packageEntity = await this.packagesService.findOne(
            packageDuration.package_id,
          );

          if (!packageEntity) {
            throw new Error(
              `Package with ID ${packageDuration.package_id} not found`,
            );
          }

          // Update user status to active and set expiration date based on package duration
          const expirationDate = new Date();
          expirationDate.setDate(
            expirationDate.getDate() + packageDuration.duration_days,
          );

          // Update user status
          await this.usersService.updateStatus(payment.user.id, 'active');

          // Assign package to user with duration_id
          await this.packagesService.assignToUser(
            packageDuration.package_id,
            payment.user.id,
            expirationDate,
            payment.package_duration_id,
          );

          // No need to update expired_date anymore as we're using PackageUser
        } catch (error) {
          this.logger.error(
            `Failed to assign package to user: ${error.message}`,
            error.stack,
          );
          // Don't throw error here, we still want to complete the payment process
        }

        // Send thank you email with Chrome extension instructions
        try {
          await this.sendThankYouEmail(payment.user);
          this.logger.log(
            `Successfully sent thank you email to ${payment.user.email}`,
          );
        } catch (emailError) {
          this.logger.error(
            `Failed to send thank you email to ${payment.user.email}: ${emailError.message}`,
            emailError.stack,
          );
          // Don't throw error here, we still want to complete the payment process
        }

        // Create Discord invite link and send email
        // try {
        //   if (payment.user.discord_id) {
        //     // Create invite link with VIP role
        //     const inviteLink = await this.inviteLinksService.createInviteLink(
        //       payment.user,
        //     );

        //     // Send invite email
        //     await this.inviteLinksService.sendInviteEmail(
        //       payment.user,
        //       inviteLink,
        //     );

        //     this.logger.log(
        //       `Created and sent Discord invite link for user ${payment.user.email}`,
        //     );
        //   } else {
        //     this.logger.warn(
        //       `User ${payment.user.email} does not have a Discord ID. Skipping Discord invite.`,
        //     );
        //   }
        // } catch (discordError) {
        //   this.logger.error(
        //     `Failed to create Discord invite for user ${payment.user.email}: ${discordError.message}`,
        //     discordError.stack,
        //   );
        // }

        return {
          success: true,
          transactionId: capture.result.id,
        };
      }

      throw new Error('Payment not completed');
    } catch (error) {
      throw new HttpException(
        'Failed to capture PayPal payment',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getPaymentById(id: number) {
    return this.paymentRepository.findOne({
      where: { id },
      relations: ['user', 'packageDuration', 'packageDuration.package'],
    });
  }

  async sendThankYouEmail(user: User): Promise<void> {
    const messageId = `<payment-success-${user.id}-${Date.now()}@kitsify.com>`;
    const subject = `[Kitsify] Thank you for your purchase - Get started with your Chrome extension`;

    const html = `
      <div style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="background: linear-gradient(to right, #ffffff, #f8f9fa); padding: 20px; border-radius: 8px; border: 1px solid #e9ecef;">
          <h2 style="color: #2b3481; margin-bottom: 20px; text-align: center;">Thank You for Your Purchase!</h2>
          <p style="color: #333; font-size: 16px; line-height: 1.6;">Hello <strong>${user.email}</strong>,</p>
          <p style="color: #333; font-size: 16px; line-height: 1.6;">
            Thank you for purchasing Kitsify Auto Login Tools! Your payment has been successfully processed and your account is now active.
          </p>

          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #2b3481;">
            <h3 style="color: #2b3481; margin-top: 0; margin-bottom: 15px;">🚀 Get Started in 2 Easy Steps:</h3>

            <div style="margin-bottom: 15px;">
              <strong style="color: #2b3481;">Step 1:</strong> Install the Chrome Extension
              <br>
              <a href="https://chromewebstore.google.com/detail/hbdinbncknklkkjeehlkngpbhhadenhn?utm_source=item-share-cb"
                 style="display: inline-block; background-color: #2b3481; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-top: 10px; font-weight: bold;">
                📥 Download Chrome Extension
              </a>
            </div>

            <div>
              <strong style="color: #2b3481;">Step 2:</strong> Start Using the Tools
              <br>
              <a href="https://kitsify.com/tools"
                 style="display: inline-block; background-color: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-top: 10px; font-weight: bold;">
                🛠️ Access Your Tools
              </a>
            </div>
          </div>

          <p style="color: #333; font-size: 16px; line-height: 1.6;">
            Once you've installed the extension and visited the tools page, you'll have access to all the premium features included in your package.
          </p>

          <p style="color: #333; font-size: 16px; line-height: 1.6;">
            If you have any questions or need assistance, please don't hesitate to contact our support team.
          </p>

          <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e9ecef; text-align: center;">
            <p style="color: #666; font-size: 14px; margin: 0;">
              Best regards,<br>
              <strong>The Kitsify Team</strong>
            </p>
          </div>
        </div>
      </div>
    `;

    try {
      await this.mailerService.sendMail({
        to: user.email,
        from: {
          name: 'Kitsify',
          address: this.configService.get('mail.from'),
        },
        subject,
        html,
        headers: {
          'Message-ID': messageId,
          'List-Unsubscribe': `<https://kitsify.com/unsubscribe?uid=${user.id}>, <mailto:<EMAIL>>`,
          'List-Unsubscribe-Post': 'List-Unsubscribe=One-Click',
          'Feedback-ID': `payment-success:${user.id}:kitsify:${process.env.MAIL_FEEDBACK_ID}`,
          Precedence: 'bulk',
          'X-Auto-Response-Suppress': 'OOF, AutoReply',
          'X-Entity-Ref-ID': `kitsify-payment-success-${user.id}`,
        },
      });
      this.logger.log(`Successfully sent thank you email to ${user.email}`);
    } catch (error) {
      this.logger.error(
        `Failed to send thank you email to ${user.email}:`,
        error,
      );
      throw error;
    }
  }
}
