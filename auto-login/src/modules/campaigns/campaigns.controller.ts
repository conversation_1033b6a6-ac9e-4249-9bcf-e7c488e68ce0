import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Req,
  ParseIntPipe,
  HttpException,
  HttpStatus,
  Res,
} from '@nestjs/common';
import { Response } from 'express';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { RolesGuard } from '../auth/roles.guard';
import { Roles } from '../auth/roles.decorator';
import { CampaignsService } from './campaigns.service';
import { CreateCampaignDto } from './dto/create-campaign.dto';
import { UpdateCampaignDto } from './dto/update-campaign.dto';
import { CampaignFilterDto } from './dto/campaign-filter.dto';

@Controller('campaigns')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles('admin')
export class CampaignsController {
  constructor(private readonly campaignsService: CampaignsService) {}

  @Post()
  async create(@Body() createCampaignDto: CreateCampaignDto, @Req() req) {
    try {
      return await this.campaignsService.create(createCampaignDto, req.user.id);
    } catch (error) {
      throw new HttpException(
        'Failed to create campaign: ' + (error.message || error),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get()
  async findAll(@Query() filterDto: CampaignFilterDto) {
    try {
      return await this.campaignsService.findAll(filterDto);
    } catch (error) {
      throw new HttpException(
        'Failed to fetch campaigns: ' + (error.message || error),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':id')
  async findOne(@Param('id', ParseIntPipe) id: number) {
    try {
      return await this.campaignsService.findOne(id);
    } catch (error) {
      throw new HttpException(
        'Failed to fetch campaign: ' + (error.message || error),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':id/stats')
  async getCampaignStats(@Param('id', ParseIntPipe) id: number) {
    try {
      return await this.campaignsService.getCampaignStats(id);
    } catch (error) {
      throw new HttpException(
        'Failed to fetch campaign stats: ' + (error.message || error),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Patch(':id')
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateCampaignDto: UpdateCampaignDto,
  ) {
    try {
      return await this.campaignsService.update(id, updateCampaignDto);
    } catch (error) {
      throw new HttpException(
        'Failed to update campaign: ' + (error.message || error),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post(':id/send')
  async sendCampaign(@Param('id', ParseIntPipe) id: number) {
    try {
      await this.campaignsService.sendCampaign(id);
      return { message: 'Campaign sending started successfully' };
    } catch (error) {
      throw new HttpException(
        'Failed to send campaign: ' + (error.message || error),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Delete(':id')
  async remove(@Param('id', ParseIntPipe) id: number) {
    try {
      await this.campaignsService.remove(id);
      return { message: 'Campaign deleted successfully' };
    } catch (error) {
      throw new HttpException(
        'Failed to delete campaign: ' + (error.message || error),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}

// Public controller for tracking
@Controller('campaigns/track')
export class CampaignTrackingController {
  constructor(private readonly campaignsService: CampaignsService) {}

  @Get(':trackingId')
  async trackEmailOpen(
    @Param('trackingId') trackingId: string,
    @Res() res: Response,
  ) {
    try {
      await this.campaignsService.trackEmailOpen(trackingId);
      
      // Return a 1x1 transparent pixel
      const pixel = Buffer.from(
        'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
        'base64'
      );
      
      res.set({
        'Content-Type': 'image/png',
        'Content-Length': pixel.length,
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
      });
      
      res.send(pixel);
    } catch (error) {
      // Return empty pixel even on error to avoid breaking email display
      const pixel = Buffer.from(
        'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
        'base64'
      );
      
      res.set({
        'Content-Type': 'image/png',
        'Content-Length': pixel.length,
      });
      
      res.send(pixel);
    }
  }
}
