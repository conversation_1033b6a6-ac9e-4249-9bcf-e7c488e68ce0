import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Req,
  ParseIntPipe,
  HttpException,
  HttpStatus,
  Res,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { Response } from 'express';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { RolesGuard } from '../auth/roles.guard';
import { Roles } from '../auth/roles.decorator';
import { CampaignsService } from './campaigns.service';
import { CreateCampaignDto } from './dto/create-campaign.dto';
import { UpdateCampaignDto } from './dto/update-campaign.dto';
import { CampaignFilterDto } from './dto/campaign-filter.dto';
import { diskStorage } from 'multer';
import { extname } from 'path';
import * as fs from 'fs';
import * as path from 'path';

@Controller('campaigns')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles('admin')
export class CampaignsController {
  constructor(private readonly campaignsService: CampaignsService) {}

  @Post()
  async create(@Body() createCampaignDto: CreateCampaignDto, @Req() req) {
    try {
      return await this.campaignsService.create(createCampaignDto, req.user.id);
    } catch (error) {
      throw new HttpException(
        'Failed to create campaign: ' + (error.message || error),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get()
  async findAll(@Query() filterDto: CampaignFilterDto) {
    try {
      return await this.campaignsService.findAll(filterDto);
    } catch (error) {
      throw new HttpException(
        'Failed to fetch campaigns: ' + (error.message || error),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':id')
  async findOne(@Param('id', ParseIntPipe) id: number) {
    try {
      return await this.campaignsService.findOne(id);
    } catch (error) {
      throw new HttpException(
        'Failed to fetch campaign: ' + (error.message || error),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':id/stats')
  async getCampaignStats(@Param('id', ParseIntPipe) id: number) {
    try {
      return await this.campaignsService.getCampaignStats(id);
    } catch (error) {
      throw new HttpException(
        'Failed to fetch campaign stats: ' + (error.message || error),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Patch(':id')
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateCampaignDto: UpdateCampaignDto,
  ) {
    try {
      return await this.campaignsService.update(id, updateCampaignDto);
    } catch (error) {
      throw new HttpException(
        'Failed to update campaign: ' + (error.message || error),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post(':id/send')
  async sendCampaign(@Param('id', ParseIntPipe) id: number) {
    try {
      await this.campaignsService.sendCampaign(id);
      return { message: 'Campaign sending started successfully' };
    } catch (error) {
      throw new HttpException(
        'Failed to send campaign: ' + (error.message || error),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Delete(':id')
  async remove(@Param('id', ParseIntPipe) id: number) {
    try {
      await this.campaignsService.remove(id);
      return { message: 'Campaign deleted successfully' };
    } catch (error) {
      throw new HttpException(
        'Failed to delete campaign: ' + (error.message || error),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('upload-image')
  @UseInterceptors(
    FileInterceptor('image', {
      storage: diskStorage({
        destination: (req, file, cb) => {
          const uploadPath = path.join(process.cwd(), 'uploads', 'campaign-images');
          if (!fs.existsSync(uploadPath)) {
            fs.mkdirSync(uploadPath, { recursive: true });
          }
          cb(null, uploadPath);
        },
        filename: (req, file, cb) => {
          const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
          cb(null, `campaign-${uniqueSuffix}${extname(file.originalname)}`);
        },
      }),
      fileFilter: (req, file, cb) => {
        if (!file.mimetype.match(/\/(jpg|jpeg|png|gif|webp)$/)) {
          return cb(new Error('Only image files are allowed!'), false);
        }
        cb(null, true);
      },
      limits: {
        fileSize: 5 * 1024 * 1024, // 5MB limit
      },
    }),
  )
  async uploadImage(@UploadedFile() file: Express.Multer.File, @Req() req) {
    try {
      if (!file) {
        throw new HttpException('No file uploaded', HttpStatus.BAD_REQUEST);
      }

      const baseUrl = process.env.APP_URL || 'http://localhost:3000';
      const imageUrl = `${baseUrl}/uploads/campaign-images/${file.filename}`;

      return {
        success: true,
        imageUrl,
        filename: file.filename,
        originalName: file.originalname,
        size: file.size,
      };
    } catch (error) {
      throw new HttpException(
        'Failed to upload image: ' + (error.message || error),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}

// Public controller for tracking
@Controller('campaigns/track')
export class CampaignTrackingController {
  constructor(private readonly campaignsService: CampaignsService) {}

  @Get(':trackingId')
  async trackEmailOpen(
    @Param('trackingId') trackingId: string,
    @Res() res: Response,
  ) {
    try {
      await this.campaignsService.trackEmailOpen(trackingId);
      
      // Return a 1x1 transparent pixel
      const pixel = Buffer.from(
        'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
        'base64'
      );
      
      res.set({
        'Content-Type': 'image/png',
        'Content-Length': pixel.length,
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
      });
      
      res.send(pixel);
    } catch (error) {
      // Return empty pixel even on error to avoid breaking email display
      const pixel = Buffer.from(
        'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
        'base64'
      );
      
      res.set({
        'Content-Type': 'image/png',
        'Content-Length': pixel.length,
      });
      
      res.send(pixel);
    }
  }
}
