import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { MailerService } from '@nestjs-modules/mailer';
import { ConfigService } from '@nestjs/config';
import { Cron, CronExpression } from '@nestjs/schedule';
import { v4 as uuidv4 } from 'uuid';

import { Campaign, CampaignStatus, TargetAudience } from './entities/campaign.entity';
import { CampaignRecipient, RecipientStatus } from './entities/campaign-recipient.entity';
import { User } from '../users/entities/user.entity';
import { CreateCampaignDto } from './dto/create-campaign.dto';
import { UpdateCampaignDto } from './dto/update-campaign.dto';
import { CampaignFilterDto } from './dto/campaign-filter.dto';
import { BatchSendResult, CampaignStats } from './interfaces/campaign.interface';

@Injectable()
export class CampaignsService {
  private readonly logger = new Logger(CampaignsService.name);
  private readonly batchSize = 50; // Send emails in batches of 50
  private readonly delayBetweenBatches = 2000; // 2 seconds delay between batches

  constructor(
    @InjectRepository(Campaign)
    private campaignRepository: Repository<Campaign>,
    @InjectRepository(CampaignRecipient)
    private recipientRepository: Repository<CampaignRecipient>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    private mailerService: MailerService,
    private configService: ConfigService,
  ) {}

  async create(createCampaignDto: CreateCampaignDto, createdBy: number): Promise<Campaign> {
    const campaign = this.campaignRepository.create({
      ...createCampaignDto,
      created_by: createdBy,
      scheduled_at: createCampaignDto.scheduled_at ? new Date(createCampaignDto.scheduled_at) : null,
    });

    const savedCampaign = await this.campaignRepository.save(campaign);
    
    // Generate recipients based on target audience
    await this.generateRecipients(savedCampaign);
    
    return this.findOne(savedCampaign.id);
  }

  async findAll(filterDto: CampaignFilterDto) {
    const { search, status, page = 1, limit = 10 } = filterDto;
    const queryBuilder = this.campaignRepository.createQueryBuilder('campaign')
      .leftJoinAndSelect('campaign.creator', 'creator')
      .orderBy('campaign.created_at', 'DESC');

    if (search) {
      queryBuilder.andWhere(
        '(campaign.name ILIKE :search OR campaign.subject ILIKE :search)',
        { search: `%${search}%` }
      );
    }

    if (status) {
      queryBuilder.andWhere('campaign.status = :status', { status });
    }

    const [campaigns, total] = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    return {
      data: campaigns,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: number): Promise<Campaign> {
    const campaign = await this.campaignRepository.findOne({
      where: { id },
      relations: ['creator', 'recipients'],
    });

    if (!campaign) {
      throw new NotFoundException(`Campaign with ID ${id} not found`);
    }

    return campaign;
  }

  async update(id: number, updateCampaignDto: UpdateCampaignDto): Promise<Campaign> {
    const campaign = await this.findOne(id);

    if (campaign.status === CampaignStatus.SENT) {
      throw new BadRequestException('Cannot update a campaign that has already been sent');
    }

    const updateData = {
      ...updateCampaignDto,
      scheduled_at: updateCampaignDto.scheduled_at ? new Date(updateCampaignDto.scheduled_at) : campaign.scheduled_at,
    };

    await this.campaignRepository.update(id, updateData);

    // If target audience changed, regenerate recipients
    if (updateCampaignDto.target_audience && updateCampaignDto.target_audience !== campaign.target_audience) {
      await this.generateRecipients(campaign);
    }

    return this.findOne(id);
  }

  async remove(id: number): Promise<void> {
    const campaign = await this.findOne(id);

    if (campaign.status === CampaignStatus.SENDING) {
      throw new BadRequestException('Cannot delete a campaign that is currently being sent');
    }

    await this.campaignRepository.remove(campaign);
  }

  async sendCampaign(id: number): Promise<void> {
    const campaign = await this.findOne(id);

    if (campaign.status !== CampaignStatus.DRAFT && campaign.status !== CampaignStatus.SCHEDULED) {
      throw new BadRequestException('Campaign cannot be sent in its current status');
    }

    // Update status to sending
    await this.campaignRepository.update(id, { 
      status: CampaignStatus.SENDING,
      sent_at: new Date(),
    });

    // Start sending emails in background
    this.sendEmailsInBatches(campaign).catch(error => {
      this.logger.error(`Failed to send campaign ${id}:`, error);
    });
  }

  private async generateRecipients(campaign: Campaign): Promise<void> {
    // Clear existing recipients
    await this.recipientRepository.delete({ campaign_id: campaign.id });

    const users = await this.getTargetUsers(campaign.target_audience);
    
    const recipients = users.map(user => ({
      campaign_id: campaign.id,
      user_id: user.id,
      email: user.email,
      tracking_id: uuidv4(),
    }));

    if (recipients.length > 0) {
      await this.recipientRepository.save(recipients);
    }

    // Update campaign recipient count
    await this.campaignRepository.update(campaign.id, {
      total_recipients: recipients.length,
    });
  }

  private async getTargetUsers(targetAudience: TargetAudience): Promise<User[]> {
    const queryBuilder = this.userRepository.createQueryBuilder('user')
      .where('user.role = :role', { role: 'user' });

    switch (targetAudience) {
      case TargetAudience.ACTIVE:
        queryBuilder.andWhere('user.status = :status', { status: 'active' });
        break;
      case TargetAudience.INACTIVE:
        queryBuilder.andWhere('user.status = :status', { status: 'inactive' });
        break;
      case TargetAudience.TRIAL:
        queryBuilder.andWhere('user.status = :status', { status: 'trial' });
        break;
      case TargetAudience.EXCLUDE_TRIAL:
        queryBuilder.andWhere('user.status != :status', { status: 'trial' });
        break;
      case TargetAudience.EXCLUDE_ACTIVE:
        queryBuilder.andWhere('user.status != :status', { status: 'active' });
        break;
      case TargetAudience.EXCLUDE_INACTIVE:
        queryBuilder.andWhere('user.status != :status', { status: 'inactive' });
        break;
      case TargetAudience.ALL:
      default:
        // No additional filtering
        break;
    }

    return queryBuilder.getMany();
  }

  private async sendEmailsInBatches(campaign: Campaign): Promise<void> {
    const recipients = await this.recipientRepository.find({
      where: {
        campaign_id: campaign.id,
        status: RecipientStatus.PENDING,
      },
      relations: ['user'],
    });

    let sentCount = 0;
    let failedCount = 0;

    // Process recipients in batches
    for (let i = 0; i < recipients.length; i += this.batchSize) {
      const batch = recipients.slice(i, i + this.batchSize);

      const batchResult = await this.sendEmailBatch(campaign, batch);
      sentCount += batchResult.success;
      failedCount += batchResult.failed;

      // Log progress
      this.logger.log(
        `Campaign ${campaign.id}: Processed batch ${Math.floor(i / this.batchSize) + 1}/${Math.ceil(recipients.length / this.batchSize)}. ` +
        `Sent: ${batchResult.success}, Failed: ${batchResult.failed}`
      );

      // Delay between batches to avoid overwhelming the email service
      if (i + this.batchSize < recipients.length) {
        await new Promise(resolve => setTimeout(resolve, this.delayBetweenBatches));
      }
    }

    // Update campaign status and counts
    await this.campaignRepository.update(campaign.id, {
      status: CampaignStatus.SENT,
      sent_count: sentCount,
      failed_count: failedCount,
    });

    this.logger.log(
      `Campaign ${campaign.id} completed. Total sent: ${sentCount}, Failed: ${failedCount}`
    );
  }

  private async sendEmailBatch(campaign: Campaign, recipients: CampaignRecipient[]): Promise<BatchSendResult> {
    let success = 0;
    let failed = 0;
    const errors: string[] = [];

    const sendPromises = recipients.map(async (recipient) => {
      try {
        await this.sendSingleEmail(campaign, recipient);

        // Update recipient status
        await this.recipientRepository.update(recipient.id, {
          status: RecipientStatus.SENT,
          sent_at: new Date(),
        });

        success++;
      } catch (error) {
        const errorMessage = error.message || 'Unknown error';
        errors.push(`${recipient.email}: ${errorMessage}`);

        // Update recipient status with error
        await this.recipientRepository.update(recipient.id, {
          status: RecipientStatus.FAILED,
          error_message: errorMessage,
        });

        failed++;
        this.logger.error(`Failed to send email to ${recipient.email}:`, error);
      }
    });

    await Promise.allSettled(sendPromises);

    return { success, failed, errors };
  }

  private async sendSingleEmail(campaign: Campaign, recipient: CampaignRecipient): Promise<void> {
    const messageId = `<campaign-${campaign.id}-${recipient.id}-${Date.now()}@kitsify.com>`;
    const trackingUrl = `${this.configService.get('APP_URL')}/campaigns/track/${recipient.tracking_id}`;

    // Replace variables in HTML template
    const htmlContent = this.replaceEmailVariables(campaign.html_template, {
      user_email: recipient.email,
      user_id: recipient.user_id,
      tracking_url: trackingUrl,
      unsubscribe_url: `https://kitsify.com/unsubscribe?uid=${recipient.user_id}`,
    });

    const mailFrom = this.configService.get('mail.from');

    const emailOptions = {
      to: recipient.email,
      from: {
        name: 'Kitsify',
        address: mailFrom,
      },
      subject: campaign.subject,
      html: htmlContent,
      headers: {
        'Message-ID': messageId,
        'List-Unsubscribe': `<https://kitsify.com/unsubscribe?uid=${recipient.user_id}>, <mailto:<EMAIL>>`,
        'List-Unsubscribe-Post': 'List-Unsubscribe=One-Click',
        'Feedback-ID': `campaign:${campaign.id}:kitsify:${process.env.MAIL_FEEDBACK_ID}`,
        'Precedence': 'bulk',
        'X-Auto-Response-Suppress': 'OOF, AutoReply',
        'X-Entity-Ref-ID': `kitsify-campaign-${campaign.id}-${recipient.id}`,
      },
    };

    await this.mailerService.sendMail(emailOptions);
  }

  private replaceEmailVariables(template: string, variables: Record<string, any>): string {
    let result = template;

    Object.entries(variables).forEach(([key, value]) => {
      const regex = new RegExp(`{{\\s*${key}\\s*}}`, 'g');
      result = result.replace(regex, String(value));
    });

    return result;
  }

  async getCampaignStats(id: number): Promise<CampaignStats> {
    const campaign = await this.findOne(id);

    const stats = await this.recipientRepository
      .createQueryBuilder('recipient')
      .select([
        'COUNT(*) as total_recipients',
        'SUM(CASE WHEN status = :sent THEN 1 ELSE 0 END) as sent_count',
        'SUM(CASE WHEN status = :failed THEN 1 ELSE 0 END) as failed_count',
        'SUM(CASE WHEN opened_at IS NOT NULL THEN 1 ELSE 0 END) as opened_count',
      ])
      .where('recipient.campaign_id = :campaignId', { campaignId: id })
      .setParameters({ sent: RecipientStatus.SENT, failed: RecipientStatus.FAILED })
      .getRawOne();

    const totalRecipients = parseInt(stats.total_recipients) || 0;
    const sentCount = parseInt(stats.sent_count) || 0;
    const failedCount = parseInt(stats.failed_count) || 0;
    const openedCount = parseInt(stats.opened_count) || 0;

    return {
      total_recipients: totalRecipients,
      sent_count: sentCount,
      failed_count: failedCount,
      open_rate: sentCount > 0 ? (openedCount / sentCount) * 100 : 0,
      delivery_rate: totalRecipients > 0 ? (sentCount / totalRecipients) * 100 : 0,
    };
  }

  // Cron job to send scheduled campaigns
  @Cron(CronExpression.EVERY_MINUTE)
  async handleScheduledCampaigns(): Promise<void> {
    const now = new Date();

    const scheduledCampaigns = await this.campaignRepository.find({
      where: {
        status: CampaignStatus.SCHEDULED,
      },
    });

    for (const campaign of scheduledCampaigns) {
      if (campaign.scheduled_at && campaign.scheduled_at <= now) {
        this.logger.log(`Sending scheduled campaign: ${campaign.id}`);
        await this.sendCampaign(campaign.id);
      }
    }
  }

  async trackEmailOpen(trackingId: string): Promise<void> {
    const recipient = await this.recipientRepository.findOne({
      where: { tracking_id: trackingId },
    });

    if (recipient && !recipient.opened_at) {
      await this.recipientRepository.update(recipient.id, {
        opened_at: new Date(),
      });
    }
  }
}
