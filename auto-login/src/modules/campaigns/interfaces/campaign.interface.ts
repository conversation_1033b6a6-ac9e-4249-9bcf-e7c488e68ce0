import { User } from '../../users/entities/user.entity';

export interface EmailRecipient {
  id: number;
  email: string;
  status: string;
}

export interface CampaignStats {
  total_recipients: number;
  sent_count: number;
  failed_count: number;
  open_rate?: number;
  delivery_rate: number;
}

export interface EmailTemplate {
  subject: string;
  html: string;
  variables?: Record<string, any>;
}

export interface BatchSendResult {
  success: number;
  failed: number;
  errors: string[];
}
