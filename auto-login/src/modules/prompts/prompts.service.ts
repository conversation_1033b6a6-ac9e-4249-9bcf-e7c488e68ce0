import { Injectable, NotFoundException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource, In } from 'typeorm';
import { Prompt } from './entities/prompt.entity';
import { PromptCategory } from './entities/prompt-category.entity';
import { PromptCategoryTranslation } from './entities/prompt-category-translation.entity';
import { Topic } from './entities/topic.entity';
import { PromptHistory } from './entities/prompt-history.entity';
import { PromptTranslation } from './entities/prompt-translation.entity';
import { CreatePromptDto } from './dto/create-prompt.dto';
import { CreatePromptCategoryDto } from './dto/create-prompt-category.dto';
import { QueryPromptsDto } from './dto/query-prompts.dto';
import { QueryPromptHistoriesDto } from './dto/query-prompt-histories.dto';
import { GeneratePromptDto } from './dto/generate-prompt.dto';
import { TranslationService } from './services/translation.service';

@Injectable()
export class PromptsService {
  private readonly logger = new Logger(PromptsService.name);

  constructor(
    @InjectRepository(Prompt)
    private promptRepository: Repository<Prompt>,
    @InjectRepository(PromptCategory)
    private promptCategoryRepository: Repository<PromptCategory>,
    @InjectRepository(PromptCategoryTranslation)
    private promptCategoryTranslationRepository: Repository<PromptCategoryTranslation>,
    @InjectRepository(Topic)
    private topicRepository: Repository<Topic>,
    @InjectRepository(PromptHistory)
    private promptHistoryRepository: Repository<PromptHistory>,
    @InjectRepository(PromptTranslation)
    private promptTranslationRepository: Repository<PromptTranslation>,
    private dataSource: DataSource,
    private translationService: TranslationService,
  ) {}

  // Prompt Categories
  async findAllCategories(lang?: string): Promise<PromptCategory[]> {
    const categories = await this.promptCategoryRepository.find({
      order: { created_at: 'DESC' },
    });

    // If no language specified or Vietnamese, return original categories
    if (!lang || lang === 'vi') {
      return categories;
    }

    // Get translations for the specified language
    const translations = await this.promptCategoryTranslationRepository.find({
      where: { lang },
    });

    // Create a map for quick lookup
    const translationMap = new Map();
    translations.forEach((translation) => {
      translationMap.set(translation.category_id, translation);
    });

    // Apply translations to categories
    return categories.map((category) => {
      const translation = translationMap.get(category.id);
      if (translation) {
        return {
          ...category,
          name: translation.title,
          description: translation.short_description || category.description,
        };
      }
      return category;
    });
  }

  async createCategory(
    createPromptCategoryDto: CreatePromptCategoryDto,
  ): Promise<PromptCategory> {
    const category = this.promptCategoryRepository.create(
      createPromptCategoryDto,
    );
    return this.promptCategoryRepository.save(category);
  }

  async findCategoryById(id: number): Promise<PromptCategory> {
    const category = await this.promptCategoryRepository.findOne({
      where: { id },
      relations: ['prompts'],
    });
    if (!category) {
      throw new NotFoundException(`Category with ID ${id} not found`);
    }
    return category;
  }

  // Prompts
  async findPromptsByCategory(queryDto: QueryPromptsDto) {
    const {
      page = 1,
      pageSize = 12,
      category_id,
      search_text,
      lang,
    } = queryDto;
    const skip = (page - 1) * pageSize;

    const queryBuilder = this.promptRepository
      .createQueryBuilder('prompt')
      .leftJoinAndSelect('prompt.category', 'category')
      .leftJoinAndSelect('prompt.topic', 'topic');

    if (category_id) {
      queryBuilder.where('prompt.category_id = :category_id', { category_id });
    }

    if (search_text && search_text.trim()) {
      queryBuilder.andWhere(
        '(prompt.title ILIKE :search OR prompt.short_description ILIKE :search OR prompt.optimization_guide ILIKE :search)',
        { search: `%${search_text.trim()}%` },
      );
    }

    const [prompts, total] = await queryBuilder
      .orderBy('prompt.created_at', 'DESC')
      .skip(skip)
      .take(pageSize)
      .getManyAndCount();

    // Apply category and prompt translations if language is specified and not Vietnamese
    let processedPrompts = prompts;
    if (lang && lang !== 'vi') {
      // Get category translations for the specified language
      const categoryIds = [
        ...new Set(prompts.map((p) => p.category_id).filter(Boolean)),
      ];
      const categoryTranslations =
        categoryIds.length > 0
          ? await this.promptCategoryTranslationRepository.find({
              where: {
                category_id: In(categoryIds),
                lang,
              },
            })
          : [];

      const categoryTranslationMap = new Map();
      categoryTranslations.forEach((translation) => {
        categoryTranslationMap.set(translation.category_id, translation);
      });

      // Get prompt translations for the specified language
      const promptIds = prompts.map((p) => p.id);
      const promptTranslations =
        promptIds.length > 0
          ? await this.promptTranslationRepository.find({
              where: {
                prompt_id: In(promptIds),
                lang,
              },
            })
          : [];

      const promptTranslationMap = new Map();
      promptTranslations.forEach((translation) => {
        promptTranslationMap.set(translation.prompt_id, translation);
      });

      // Identify prompts that need translation
      const promptsNeedingTranslation = prompts.filter(
        (prompt) => !promptTranslationMap.has(prompt.id),
      );

      // Create translations for prompts that don't have them using optimized bulk translation
      if (promptsNeedingTranslation.length > 0) {
        this.logger.log(
          `Creating translations for ${promptsNeedingTranslation.length} prompts to ${lang}`,
        );

        // Use optimized bulk translation for better performance
        await this.translatePromptsBulkOptimized(
          promptsNeedingTranslation,
          lang,
          promptTranslationMap,
        );
      }

      // Process prompts with translations
      processedPrompts = prompts.map((prompt) => {
        const translatedPrompt = { ...prompt };

        // Apply category translation
        if (prompt.category) {
          const categoryTranslation = categoryTranslationMap.get(
            prompt.category.id,
          );
          if (categoryTranslation) {
            translatedPrompt.category = {
              ...prompt.category,
              name: categoryTranslation.title,
              description:
                categoryTranslation.short_description ||
                prompt.category.description,
            };
          }
        }

        // Apply prompt translation
        const translation = promptTranslationMap.get(prompt.id);
        if (translation) {
          translatedPrompt.title = translation.title || prompt.title;
          translatedPrompt.short_description =
            translation.short_description || prompt.short_description;
        }

        return translatedPrompt;
      });
    }

    // Transform to simplified response format
    const simplifiedPrompts = processedPrompts.map((prompt) => ({
      id: prompt.id,
      title: prompt.title,
      short_description: prompt.short_description,
    }));

    return {
      data: simplifiedPrompts,
      pagination: {
        page,
        pageSize,
        total,
        totalPages: Math.ceil(total / pageSize),
      },
    };
  }

  async findPromptById(id: number, lang?: string): Promise<Prompt> {
    const prompt = await this.promptRepository.findOne({
      where: { id },
    });

    if (!prompt) {
      throw new NotFoundException(`Prompt with ID ${id} not found`);
    }

    if (!lang || lang === 'vi') {
      return prompt;
    }

    let translation = await this.promptTranslationRepository.findOne({
      where: { prompt_id: id, lang },
    });

    // We need a full translation. If we don't have one, or the one we have is partial,
    // (indicated by null optimization_guide when original has one), then we (re-)translate.
    if (
      !translation ||
      (prompt.optimization_guide && !translation.optimization_guide)
    ) {
      try {
        this.logger.log(
          `Creating or updating full translation for prompt ${id} to ${lang}`,
        );
        translation = await this.translateAndSaveFullPrompt(prompt, lang);
      } catch (error) {
        this.logger.error(
          `Full translation failed for prompt ${id} to ${lang}: ${error.message}`,
        );
        // If translation fails, we can still proceed with an old partial translation
        // or the original prompt. The 'translation' variable will be what it was before the try block.
      }
    }

    if (translation) {
      return {
        ...prompt,
        title: translation.title ?? prompt.title,
        short_description:
          translation.short_description ?? prompt.short_description,
        optimization_guide:
          translation.optimization_guide ?? prompt.optimization_guide,
      };
    }

    // Fallback for when no translation exists and creation failed
    return prompt;
  }

  async createPrompt(createPromptDto: CreatePromptDto): Promise<Prompt> {
    const prompt = this.promptRepository.create(createPromptDto);
    return this.promptRepository.save(prompt);
  }

  async findAllPrompts(): Promise<Prompt[]> {
    return this.promptRepository.find({
      relations: ['category', 'topic'],
      order: { created_at: 'DESC' },
    });
  }

  // Topics
  async findAllTopics(): Promise<Topic[]> {
    return this.topicRepository.find({
      order: { created_at: 'DESC' },
    });
  }

  // Generate prompt result using OpenRouter
  async generatePromptResult(generateDto: GeneratePromptDto, userId: number) {
    const {
      prompt_text,
      model = 'google/gemini-2.0-flash-exp:free',
      prompt_id,
    } = generateDto;

    try {
      const response = await fetch(
        'https://openrouter.ai/api/v1/chat/completions',
        {
          method: 'POST',
          headers: {
            Authorization: `Bearer ${process.env.OPENROUTER_API_KEY}`,
            'Content-Type': 'application/json',
            'X-Title': 'Kitsify Prompts',
          },
          body: JSON.stringify({
            model: model,
            messages: [
              {
                role: 'user',
                content: prompt_text,
              },
            ],
            max_tokens: 2000,
            temperature: 0.7,
          }),
        },
      );

      if (!response.ok) {
        throw new Error(
          `OpenRouter API error: ${response.status} ${response.statusText}`,
        );
      }

      const data = await response.json();
      const result =
        data.choices[0]?.message?.content || 'No response generated';

      // Save to history
      const historyEntry = this.promptHistoryRepository.create({
        user_id: userId,
        prompt_id: prompt_id || null,
        generated_result: result,
        model: model,
        usage: data.usage,
      });

      await this.promptHistoryRepository.save(historyEntry);

      return {
        result: result,
        model: model,
        usage: data.usage,
      };
    } catch (error) {
      console.error('Error calling OpenRouter API:', error);
      throw new Error('Failed to generate prompt result: ' + error.message);
    }
  }

  // Prompt Histories
  async findPromptHistoriesByUser(
    userId: number,
    queryDto: QueryPromptHistoriesDto,
  ) {
    const { page = 1, pageSize = 20, prompt_id, search_text } = queryDto;
    const skip = (page - 1) * pageSize;

    const queryBuilder = this.promptHistoryRepository
      .createQueryBuilder('history')
      .leftJoinAndSelect('history.prompt', 'prompt')
      .where('history.user_id = :userId', { userId });

    if (prompt_id) {
      queryBuilder.andWhere('history.prompt_id = :prompt_id', { prompt_id });
    }

    if (search_text && search_text.trim()) {
      queryBuilder.andWhere('history.generated_result ILIKE :search', {
        search: `%${search_text.trim()}%`,
      });
    }

    const histories = await queryBuilder
      .orderBy('history.created_at', 'DESC')
      .skip(skip)
      .take(pageSize)
      .getMany();

    return {
      data: histories.map((history) => ({
        id: history.id,
        user_id: history.user_id,
        prompt_id: history.prompt_id,
        generated_result: history.generated_result,
        model: history.model,
        usage: history.usage,
        created_at: history.created_at,
        prompt: history.prompt
          ? {
              id: history.prompt.id,
              title: history.prompt.title,
            }
          : null,
      })),
      pagination: {
        page,
        pageSize,
        total: histories.length,
        totalPages: 1,
      },
    };
  }

  async findPromptHistoryById(
    id: number,
    userId: number,
  ): Promise<PromptHistory> {
    const history = await this.promptHistoryRepository.findOne({
      where: { id, user_id: userId },
      relations: ['prompt', 'prompt.category'],
    });

    if (!history) {
      throw new NotFoundException(`Prompt history with ID ${id} not found`);
    }

    return history;
  }

  // Helper for full translation (title, description, guide)
  private async translateAndSaveFullPrompt(
    prompt: Prompt,
    lang: string,
  ): Promise<PromptTranslation> {
    this.logger.log(
      `Starting full translation for prompt ${prompt.id} to ${lang}`,
    );

    return await this.dataSource.transaction(async (manager) => {
      // We are creating/updating a translation, so let's call the AI service first.
      const fullTranslationResponse =
        await this.translationService.translatePromptFieldsWithOptimizationGuide(
          {
            title: prompt.title,
            short_description: prompt.short_description,
            optimization_guide: prompt.optimization_guide,
            targetLanguage: lang,
          },
        );

      this.logger.log(
        `AI full translation completed for prompt ${prompt.id} to ${lang}`,
      );

      // Use upsert to either insert a new translation or update an existing one.
      await manager.upsert(
        PromptTranslation,
        {
          prompt_id: prompt.id,
          lang,
          title: fullTranslationResponse.title,
          short_description: fullTranslationResponse.short_description,
          optimization_guide: fullTranslationResponse.optimization_guide,
        },
        ['prompt_id', 'lang'], // Conflict target
      );

      this.logger.log(
        `Full translation saved/updated in DB for prompt ${prompt.id} to ${lang}`,
      );

      // Fetch and return the record to ensure we have the complete entity.
      const result = await manager.findOne(PromptTranslation, {
        where: { prompt_id: prompt.id, lang },
      });

      if (!result) {
        // This should theoretically never happen after a successful upsert.
        throw new Error(
          `Failed to find translation after upsert for prompt ${prompt.id}, lang ${lang}`,
        );
      }

      return result;
    });
  }

  // Translation helper method for findPromptsByCategory (excludes optimization_guide)
  private async translateAndSavePrompt(
    prompt: Prompt,
    lang: string,
  ): Promise<PromptTranslation> {
    this.logger.log(`Starting translation for prompt ${prompt.id} to ${lang}`);

    // Use transaction to prevent race conditions
    return await this.dataSource.transaction(async (manager) => {
      // Check again if translation exists (race condition protection)
      const existingTranslation = await manager.findOne(PromptTranslation, {
        where: { prompt_id: prompt.id, lang },
      });

      if (existingTranslation) {
        this.logger.log(
          `Found existing translation in transaction for prompt ${prompt.id} to ${lang}`,
        );
        return existingTranslation;
      }

      this.logger.log(
        `No existing translation found, calling AI service for prompt ${prompt.id} to ${lang}`,
      );

      // Translate using AI service (only title and short_description)
      const translationResponse =
        await this.translationService.translatePromptFields({
          title: prompt.title,
          short_description: prompt.short_description,
          targetLanguage: lang,
        });

      this.logger.log(
        `AI translation completed for prompt ${prompt.id} to ${lang}`,
      );
      this.logger.debug(`Translated title: ${translationResponse.title}`);

      try {
        // Use upsert to handle concurrent insertions gracefully
        const result = await manager
          .createQueryBuilder()
          .insert()
          .into(PromptTranslation)
          .values({
            prompt_id: prompt.id,
            lang,
            title: translationResponse.title,
            short_description: translationResponse.short_description,
            optimization_guide: null, // Not translating optimization_guide anymore
          })
          .orIgnore() // PostgreSQL: ON CONFLICT DO NOTHING
          .returning('*')
          .execute();

        // If insert was successful, return the new translation
        if (result.raw && result.raw.length > 0) {
          const savedTranslation = result.raw[0];
          this.logger.log(
            `Translation saved to DB for prompt ${prompt.id} to ${lang} with ID ${savedTranslation.id}`,
          );
          return savedTranslation;
        }

        // If insert was ignored (duplicate), fetch the existing translation
        const existingAfterInsert = await manager.findOne(PromptTranslation, {
          where: { prompt_id: prompt.id, lang },
        });

        if (existingAfterInsert) {
          this.logger.log(
            `Found existing translation after insert attempt for prompt ${prompt.id} to ${lang}`,
          );
          return existingAfterInsert;
        }

        // This should not happen, but handle it gracefully
        throw new Error(
          `Failed to create or find translation for prompt ${prompt.id} to ${lang}`,
        );
      } catch (error) {
        // Handle specific constraint violation error
        if (
          error.code === '23505' &&
          error.constraint === 'UQ_prompt_translations_prompt_id_lang'
        ) {
          this.logger.log(
            `Duplicate key constraint caught for prompt ${prompt.id} to ${lang}, fetching existing translation`,
          );

          // Fetch the existing translation that was created by another request
          const existingTranslation = await manager.findOne(PromptTranslation, {
            where: { prompt_id: prompt.id, lang },
          });

          if (existingTranslation) {
            return existingTranslation;
          }
        }

        // Re-throw other errors
        throw error;
      }
    });
  }

  /**
   * Optimized bulk translation method for better performance
   * Translates multiple prompts efficiently using bulk API calls
   */
  private async translatePromptsBulkOptimized(
    promptsNeedingTranslation: Prompt[],
    lang: string,
    promptTranslationMap: Map<number, PromptTranslation>,
  ): Promise<void> {
    // Optimal batch size for bulk translation (balance between API limits and efficiency)
    const bulkBatchSize = 15; // Reduced from 20 for better API stability

    for (let i = 0; i < promptsNeedingTranslation.length; i += bulkBatchSize) {
      const batch = promptsNeedingTranslation.slice(i, i + bulkBatchSize);

      try {
        // Prepare prompts for bulk translation
        const promptsForBulk = batch.map((prompt) => ({
          id: prompt.id,
          title: prompt.title,
          short_description: prompt.short_description,
        }));

        // Use bulk translation API for maximum efficiency
        const translatedPrompts =
          await this.translationService.translatePromptsBulk(
            promptsForBulk,
            lang,
          );

        // Save translations to database in a single transaction
        await this.dataSource.transaction(async (manager) => {
          const translationsToSave = translatedPrompts.map((translated) => ({
            prompt_id: translated.id,
            lang,
            title: translated.title,
            short_description: translated.short_description,
            optimization_guide: null, // Not translating optimization_guide for list view
          }));

          // Bulk insert translations
          const result = await manager
            .createQueryBuilder()
            .insert()
            .into(PromptTranslation)
            .values(translationsToSave)
            .orIgnore() // Handle duplicates gracefully
            .returning('*')
            .execute();

          // Add successful translations to map for immediate use
          if (result.raw && result.raw.length > 0) {
            result.raw.forEach((savedTranslation: PromptTranslation) => {
              promptTranslationMap.set(
                savedTranslation.prompt_id,
                savedTranslation,
              );
            });
          }
        });

        this.logger.log(
          `Successfully bulk translated and saved ${batch.length} prompts to ${lang}`,
        );
      } catch (error) {
        this.logger.error(
          `Bulk translation failed for batch starting at index ${i}:`,
          error.message,
        );

        // Fallback: try individual translations for this batch
        await this.fallbackIndividualTranslation(
          batch,
          lang,
          promptTranslationMap,
        );
      }
    }
  }

  /**
   * Fallback method for individual translations when bulk translation fails
   */
  private async fallbackIndividualTranslation(
    batch: Prompt[],
    lang: string,
    promptTranslationMap: Map<number, PromptTranslation>,
  ): Promise<void> {
    this.logger.log(
      `Falling back to individual translation for ${batch.length} prompts`,
    );

    await Promise.allSettled(
      batch.map(async (prompt) => {
        try {
          const translatedFields = await this.translateAndSavePrompt(
            prompt,
            lang,
          );
          promptTranslationMap.set(prompt.id, translatedFields);
        } catch (error) {
          this.logger.error(
            `Individual translation also failed for prompt ${prompt.id}:`,
            error.message,
          );
        }
      }),
    );
  }

  /**
   * Optimized method specifically for pagination scenarios
   * Only translates prompts that are actually being displayed
   */
  async findPromptsByCategoryOptimized(queryDto: QueryPromptsDto) {
    const {
      page = 1,
      pageSize = 12,
      category_id,
      search_text,
      lang,
    } = queryDto;
    const skip = (page - 1) * pageSize;

    const queryBuilder = this.promptRepository
      .createQueryBuilder('prompt')
      .leftJoinAndSelect('prompt.category', 'category')
      .leftJoinAndSelect('prompt.topic', 'topic');

    if (category_id) {
      queryBuilder.where('prompt.category_id = :category_id', { category_id });
    }

    if (search_text && search_text.trim()) {
      queryBuilder.andWhere(
        '(prompt.title ILIKE :search OR prompt.short_description ILIKE :search OR prompt.optimization_guide ILIKE :search)',
        { search: `%${search_text.trim()}%` },
      );
    }

    const [prompts, total] = await queryBuilder
      .orderBy('prompt.created_at', 'DESC')
      .skip(skip)
      .take(pageSize)
      .getManyAndCount();

    // Apply translations only if needed and only for current page
    let processedPrompts = prompts;
    if (lang && lang !== 'vi' && prompts.length > 0) {
      // Use optimized translation strategy
      processedPrompts = await this.applyOptimizedTranslations(prompts, lang);
    }

    // Transform to simplified response format
    const simplifiedPrompts = processedPrompts.map((prompt) => ({
      id: prompt.id,
      title: prompt.title,
      short_description: prompt.short_description,
    }));

    return {
      data: simplifiedPrompts,
      pagination: {
        page,
        pageSize,
        total,
        totalPages: Math.ceil(total / pageSize),
      },
    };
  }

  /**
   * Apply translations with maximum optimization
   */
  private async applyOptimizedTranslations(
    prompts: Prompt[],
    lang: string,
  ): Promise<Prompt[]> {
    // Step 1: Batch load existing translations
    const promptIds = prompts.map((p) => p.id);
    const existingTranslations = await this.promptTranslationRepository.find({
      where: {
        prompt_id: In(promptIds),
        lang,
      },
    });

    const translationMap = new Map<number, PromptTranslation>();
    existingTranslations.forEach((translation) => {
      translationMap.set(translation.prompt_id, translation);
    });

    // Step 2: Identify prompts needing translation
    const promptsNeedingTranslation = prompts.filter(
      (prompt) => !translationMap.has(prompt.id),
    );

    // Step 3: Translate missing prompts using bulk API (most efficient)
    if (promptsNeedingTranslation.length > 0) {
      try {
        const promptsForBulk = promptsNeedingTranslation.map((prompt) => ({
          id: prompt.id,
          title: prompt.title,
          short_description: prompt.short_description,
        }));

        // Single bulk API call for all missing translations
        const translatedPrompts =
          await this.translationService.translatePromptsBulk(
            promptsForBulk,
            lang,
          );

        // Bulk save to database
        const translationsToSave = translatedPrompts.map((translated) => ({
          prompt_id: translated.id,
          lang,
          title: translated.title,
          short_description: translated.short_description,
          optimization_guide: null,
        }));

        const savedTranslations =
          await this.promptTranslationRepository.save(translationsToSave);

        // Add to map
        savedTranslations.forEach((translation) => {
          translationMap.set(translation.prompt_id, translation);
        });

        this.logger.log(
          `Bulk translated ${promptsNeedingTranslation.length} prompts to ${lang}`,
        );
      } catch (error) {
        this.logger.error('Bulk translation failed:', error.message);
        // Continue with existing translations only
      }
    }

    // Step 4: Apply translations to prompts
    return prompts.map((prompt) => {
      const translation = translationMap.get(prompt.id);
      if (translation) {
        return {
          ...prompt,
          title: translation.title || prompt.title,
          short_description:
            translation.short_description || prompt.short_description,
        };
      }
      return prompt;
    });
  }
}
