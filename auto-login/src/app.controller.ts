import {
  <PERSON>,
  Get,
  Render,
  UseGuards,
  Param,
  ParseIntPipe,
} from '@nestjs/common';
import { AppService } from './app.service';
import { JwtAuthGuard } from './modules/auth/jwt-auth.guard';
import { RolesGuard } from './modules/auth/roles.guard';
import { Roles } from './modules/auth/roles.decorator';

@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  @Render('home/index')
  getHome() {
    return {};
  }

  // Admin account management routes
  @Get('admin/accounts')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  @Render('admin/accounts/index')
  getAdminAccounts() {
    return {};
  }

  @Get('admin/accounts/create')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  @Render('admin/accounts/create')
  getAdminAccountsCreate() {
    return {};
  }

  @Get('admin/accounts/:id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  @Render('admin/accounts/view')
  getAdminAccountsView(@Param('id', ParseIntPipe) id: number) {
    return { id };
  }

  @Get('admin/accounts/:id/edit')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  @Render('admin/accounts/edit')
  getAdminAccountsEdit(@Param('id', ParseIntPipe) id: number) {
    return { id };
  }

  // Admin package management routes
  @Get('admin/packages')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  @Render('admin/packages/index')
  getAdminPackages() {
    return {};
  }

  @Get('admin/packages/create')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  @Render('admin/packages/create')
  getAdminPackagesCreate() {
    return {};
  }

  @Get('admin/packages/:id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  @Render('admin/packages/view')
  getAdminPackagesView(@Param('id', ParseIntPipe) id: number) {
    return { id };
  }

  @Get('admin/packages/:id/edit')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  @Render('admin/packages/edit')
  getAdminPackagesEdit(@Param('id', ParseIntPipe) id: number) {
    return { id };
  }

  // Admin trial user management route
  @Get('admin/trial-users')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  @Render('admin/trial-users/index')
  getAdminTrialUsers() {
    return {};
  }

  // Admin cookie management route
  @Get('admin/cookies')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  @Render('admin/cookies/index')
  getAdminCookies() {
    return {};
  }

  // Admin campaign management routes
  @Get('admin/campaigns')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  @Render('admin/campaigns/index')
  getAdminCampaigns() {
    return {};
  }

  @Get('admin/campaigns/create')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  @Render('admin/campaigns/create')
  getAdminCampaignsCreate() {
    return {};
  }

  @Get('admin/campaigns/:id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  @Render('admin/campaigns/view')
  getAdminCampaignsView(@Param('id', ParseIntPipe) id: number) {
    return { id };
  }
}
